<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>科研管理系统实验报告</title>
    <style>
        body {
            font-family: "Microsoft YaHei", "SimSun", serif;
            line-height: 1.6;
            margin: 40px;
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        .title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 20px;
        }
        .subtitle {
            font-size: 18px;
            margin-bottom: 30px;
        }
        .info-table {
            margin: 20px auto;
            border-collapse: collapse;
            width: 60%;
        }
        .info-table td {
            border: 1px solid #000;
            padding: 8px;
            text-align: center;
        }
        h1 {
            font-size: 20px;
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 5px;
        }
        h2 {
            font-size: 18px;
            color: #34495e;
            margin-top: 25px;
        }
        h3 {
            font-size: 16px;
            color: #7f8c8d;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 15px 0;
        }
        table, th, td {
            border: 1px solid #ddd;
        }
        th, td {
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
            font-family: "Courier New", monospace;
            font-size: 14px;
            overflow-x: auto;
        }
        .abstract {
            background-color: #f8f9fa;
            padding: 20px;
            border-left: 4px solid #3498db;
            margin: 20px 0;
        }
        .keywords {
            font-weight: bold;
            margin-top: 15px;
        }
        .toc {
            background-color: #f8f9fa;
            padding: 20px;
            border: 1px solid #ddd;
            margin: 20px 0;
        }
        .toc ul {
            list-style-type: none;
            padding-left: 0;
        }
        .toc li {
            margin: 5px 0;
        }
        .toc a {
            text-decoration: none;
            color: #3498db;
        }
        .toc a:hover {
            text-decoration: underline;
        }
        .page-break {
            page-break-before: always;
        }
        @media print {
            body {
                margin: 20px;
            }
            .page-break {
                page-break-before: always;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="title">《数据库原理及应用》课程设计实验报告书</div>
        <div class="subtitle">安徽工业大学计算机科学与技术学院</div>
        <div class="subtitle" style="font-size: 20px; margin-top: 30px;">题目：科研管理系统设计与实现</div>
        
        <table class="info-table">
            <tr>
                <td style="width: 20%;">学号</td>
                <td style="width: 30%;">[请填写学号]</td>
                <td style="width: 20%;">姓名</td>
                <td style="width: 30%;">[请填写姓名]</td>
            </tr>
            <tr>
                <td>专业</td>
                <td>计算机科学与技术</td>
                <td>班级</td>
                <td>[请填写班级]</td>
            </tr>
            <tr>
                <td>指导教师</td>
                <td>[请填写指导教师]</td>
                <td>分数</td>
                <td></td>
            </tr>
        </table>
        
        <div style="margin-top: 40px; font-size: 16px;">
            <strong>日期：</strong>2024年 月
        </div>
    </div>

    <div class="page-break"></div>

    <div class="abstract">
        <h1>摘要</h1>
        <p>本课程设计选择科研管理系统作为开发目标，旨在通过实际项目开发加深对数据库原理及应用的理解。随着高等院校科研活动的日益增多，传统的人工管理方式已无法满足现代科研管理的需求，迫切需要一个高效、准确的信息化管理系统。</p>
        
        <p>本系统主要功能包括教师信息管理、科研项目管理、科研成果管理、成果排名管理、项目参与管理以及多维度的查询统计功能。系统能够实现教师基本信息的录入与维护、科研项目的全生命周期管理、科研成果的分类管理和排名统计，以及基于部门和教师的多角度数据查询分析。</p>
        
        <p>系统采用Java语言开发，使用MySQL数据库存储数据，基于Swing技术构建图形用户界面。经过测试，系统功能完整，性能稳定，用户界面友好，达到了预期的设计目标，能够有效提升科研管理的效率和准确性。</p>
        
        <div class="keywords">
            <strong>关键词：</strong>科研管理；数据库设计；Java；MySQL；Swing界面
        </div>
    </div>

    <div class="page-break"></div>

    <div class="toc">
        <h1>目录</h1>
        <ul>
            <li><a href="#section1">1 系统需求分析</a>
                <ul style="padding-left: 20px;">
                    <li><a href="#section1-1">1.1 系统开发背景</a></li>
                    <li><a href="#section1-2">1.2 系统目标</a></li>
                    <li><a href="#section1-3">1.3 功能需求</a></li>
                    <li><a href="#section1-4">1.4 非功能需求</a></li>
                    <li><a href="#section1-5">1.5 系统约束</a></li>
                </ul>
            </li>
            <li><a href="#section2">2 数据库的设计与实现</a>
                <ul style="padding-left: 20px;">
                    <li><a href="#section2-1">2.1 数据库E-R图</a></li>
                    <li><a href="#section2-2">2.2 表结构设计</a></li>
                    <li><a href="#section2-3">2.3 数据库物理结构设计</a></li>
                    <li><a href="#section2-4">2.4 数据库索引设计</a></li>
                </ul>
            </li>
            <li><a href="#section3">3 系统主要功能设计</a></li>
            <li><a href="#section4">4 系统主要功能的实现及测试</a></li>
            <li><a href="#section5">5 系统说明</a></li>
            <li><a href="#conclusion">总结</a></li>
            <li><a href="#appendix">附录：部分源代码</a></li>
        </ul>
    </div>

    <div class="page-break"></div>

    <h1 id="section1">1 系统需求分析</h1>

    <h2 id="section1-1">1.1 系统开发背景</h2>
    <p>随着高等教育事业的快速发展，高校科研活动日益频繁，科研项目数量不断增加，科研成果类型日趋多样化。传统的纸质档案管理和人工统计方式已经无法满足现代科研管理的需求，存在以下问题：</p>
    <ol>
        <li><strong>信息管理效率低下：</strong>教师信息、项目信息、成果信息分散管理，查询困难，统计工作量大。</li>
        <li><strong>数据准确性难以保证：</strong>人工录入和统计容易出错，数据一致性难以维护。</li>
        <li><strong>信息共享困难：</strong>各部门之间信息孤立，缺乏有效的信息共享机制。</li>
        <li><strong>决策支持不足：</strong>缺乏有效的数据分析和统计报表，难以为管理决策提供支持。</li>
    </ol>
    <p>因此，开发一个集成化的科研管理系统，实现科研信息的数字化管理，提高管理效率和决策水平，具有重要的现实意义。</p>

    <h2 id="section1-2">1.2 系统目标</h2>
    <p>设计并实现一个高效、易用、稳定且可扩展的科研管理系统，满足高校科研管理的日常需求，具体目标如下：</p>
    <ol>
        <li><strong>提高管理效率：</strong>通过信息化手段，简化科研管理流程，提高工作效率。</li>
        <li><strong>保证数据准确性：</strong>建立完整的数据约束机制，确保数据的准确性和一致性。</li>
        <li><strong>增强查询统计能力：</strong>提供多维度的查询和统计功能，支持管理决策。</li>
        <li><strong>改善用户体验：</strong>设计友好的用户界面，降低系统使用门槛。</li>
    </ol>

    <h2 id="section1-3">1.3 功能需求</h2>
    
    <h3>用户管理</h3>
    <ol>
        <li><strong>用户登录验证：</strong>教师通过用户名和密码登录系统，系统验证用户身份。</li>
        <li><strong>权限控制：</strong>不同用户具有不同的操作权限，确保数据安全。</li>
    </ol>

    <h3>教师信息管理</h3>
    <ol>
        <li><strong>教师信息录入：</strong>录入教师基本信息，包括姓名、性别、出生日期、民族、学历、职称、薪资等。</li>
        <li><strong>教师信息维护：</strong>支持教师信息的查询、修改、删除操作。</li>
        <li><strong>按职称查询：</strong>支持按职称筛选教师信息。</li>
    </ol>

    <h3>科研项目管理</h3>
    <ol>
        <li><strong>项目信息管理：</strong>管理项目基本信息，包括项目名称、负责人、经费等。</li>
        <li><strong>项目参与管理：</strong>管理教师参与项目的情况，记录参与角色。</li>
    </ol>

    <h3>科研成果管理</h3>
    <ol>
        <li><strong>成果信息录入：</strong>录入科研成果信息，包括成果类别、级别、等级、批准日期等。</li>
        <li><strong>成果排名管理：</strong>管理科研成果的排名信息，记录各教师在成果中的排名。</li>
        <li><strong>成果信息维护：</strong>支持成果信息的增删改查操作。</li>
    </ol>

    <h3>查询统计功能</h3>
    <ol>
        <li><strong>教师项目成果查询：</strong>查询指定教师参与的项目和获得的成果。</li>
        <li><strong>部门成果统计：</strong>统计指定部门的科研成果和经费情况。</li>
        <li><strong>综合查询：</strong>支持多条件组合查询。</li>
    </ol>

    <h2 id="section1-4">1.4 非功能需求</h2>
    
    <h3>性能需求</h3>
    <ol>
        <li><strong>响应时间：</strong>系统各项操作响应时间不超过3秒。</li>
        <li><strong>并发处理：</strong>支持多用户同时操作，保证系统稳定性。</li>
    </ol>

    <h3>安全性需求</h3>
    <ol>
        <li><strong>数据安全：</strong>采用合适的数据存储和备份策略，确保数据安全。</li>
        <li><strong>访问控制：</strong>实施用户身份验证，防止非法访问。</li>
    </ol>

    <h3>可用性需求</h3>
    <ol>
        <li><strong>界面友好：</strong>提供直观、易用的图形用户界面。</li>
        <li><strong>操作简便：</strong>操作流程简单明了，降低学习成本。</li>
    </ol>

    <h3>兼容性需求</h3>
    <ol>
        <li><strong>平台兼容：</strong>支持Windows操作系统。</li>
        <li><strong>数据库兼容：</strong>兼容MySQL数据库系统。</li>
    </ol>

    <h2 id="section1-5">1.5 系统约束</h2>
    
    <h3>技术约束</h3>
    <ol>
        <li><strong>开发语言：</strong>使用Java语言进行开发。</li>
        <li><strong>数据库系统：</strong>使用MySQL数据库管理系统。</li>
        <li><strong>用户界面：</strong>基于Java Swing技术构建图形用户界面。</li>
        <li><strong>开发环境：</strong>使用IntelliJ IDEA集成开发环境。</li>
    </ol>

    <h3>硬件约束</h3>
    <ol>
        <li><strong>服务器配置：</strong>需要安装MySQL数据库服务器。</li>
        <li><strong>客户端配置：</strong>需要安装Java运行环境（JRE）。</li>
    </ol>

    <div class="page-break"></div>

    <h1 id="section2">2 数据库的设计与实现</h1>

    <h2 id="section2-1">2.1 数据库E-R图</h2>
    <p>系统的实体关系图如下所示：</p>
    <div class="code-block">
[教师] ——— 参与 ——— [项目] ——— 产生 ——— [成果]
  |                    |                    |
  |                    |                    |
管理                  负责                 排名
  |                    |                    |
  |                    |                    |
[部门]              [经费]              [排名]
    </div>
    <p>主要实体及其关系：</p>
    <ul>
        <li><strong>教师（Teacher）：</strong>系统的核心实体，包含教师的基本信息</li>
        <li><strong>项目（Project）：</strong>科研项目实体，由教师负责管理</li>
        <li><strong>成果（Achievement）：</strong>科研成果实体，与项目关联</li>
        <li><strong>项目参与（ProjectParticipation）：</strong>教师与项目的多对多关系</li>
        <li><strong>成果排名（AchievementRanking）：</strong>教师在成果中的排名关系</li>
    </ul>

    <h2 id="section2-2">2.2 表结构设计</h2>
    
    <h3>Teacher表（教师信息表）</h3>
    <table>
        <tr>
            <th>字段名</th>
            <th>数据类型</th>
            <th>约束</th>
            <th>备注</th>
        </tr>
        <tr>
            <td>teacher_id</td>
            <td>BIGINT</td>
            <td>PRIMARY KEY, AUTO_INCREMENT</td>
            <td>教师唯一标识符</td>
        </tr>
        <tr>
            <td>username</td>
            <td>VARCHAR(50)</td>
            <td>NOT NULL, UNIQUE</td>
            <td>登录用户名</td>
        </tr>
        <tr>
            <td>password</td>
            <td>VARCHAR(100)</td>
            <td>NOT NULL</td>
            <td>登录密码</td>
        </tr>
        <tr>
            <td>name</td>
            <td>VARCHAR(100)</td>
            <td>NOT NULL</td>
            <td>教师姓名</td>
        </tr>
        <tr>
            <td>gender</td>
            <td>VARCHAR(10)</td>
            <td></td>
            <td>性别</td>
        </tr>
        <tr>
            <td>birth_date</td>
            <td>DATE</td>
            <td></td>
            <td>出生日期</td>
        </tr>
        <tr>
            <td>ethnicity</td>
            <td>VARCHAR(50)</td>
            <td></td>
            <td>民族</td>
        </tr>
        <tr>
            <td>education</td>
            <td>VARCHAR(100)</td>
            <td></td>
            <td>学历</td>
        </tr>
        <tr>
            <td>start_work_date</td>
            <td>DATE</td>
            <td></td>
            <td>参加工作日期</td>
        </tr>
        <tr>
            <td>title</td>
            <td>VARCHAR(100)</td>
            <td></td>
            <td>职称</td>
        </tr>
        <tr>
            <td>base_salary</td>
            <td>DECIMAL(10,2)</td>
            <td></td>
            <td>基本工资</td>
        </tr>
        <tr>
            <td>post_salary</td>
            <td>DECIMAL(10,2)</td>
            <td></td>
            <td>岗位工资</td>
        </tr>
        <tr>
            <td>bonus_salary</td>
            <td>DECIMAL(10,2)</td>
            <td></td>
            <td>奖金</td>
        </tr>
        <tr>
            <td>dept_id</td>
            <td>BIGINT</td>
            <td></td>
            <td>部门ID</td>
        </tr>
    </table>
</body>
</html>
