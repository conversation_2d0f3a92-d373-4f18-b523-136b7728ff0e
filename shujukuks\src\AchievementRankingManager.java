import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.sql.*;

public class AchievementRankingManager extends J<PERSON>rame {
    private JTextField achievementIdField, teacherIdField, rankingField;
    private JTable table;
    private DefaultTableModel model;
    private Teacher currentTeacher;

    public AchievementRankingManager(Teacher teacher) {
        this.currentTeacher = teacher;

        setTitle("成果排名管理");
        setSize(850, 400);
        setLocationRelativeTo(null);
        setDefaultCloseOperation(EXIT_ON_CLOSE);
        setLayout(new BorderLayout());

        // 输入区
        JPanel panel = new JPanel(new GridLayout(2, 5, 5, 5));
        panel.add(new JLabel("成果ID"));
        achievementIdField = new JTextField();
        panel.add(achievementIdField);

        panel.add(new JLabel("教师ID"));
        teacherIdField = new JTextField();
        panel.add(teacherIdField);

        panel.add(new JLabel("排名"));
        rankingField = new JTextField();
        panel.add(rankingField);

        JButton addBtn = new JButton("添加排名");
        JButton deleteBtn = new JButton("删除排名");
        JButton refreshBtn = new JButton("刷新");
        JButton backBtn = new JButton("返回主菜单");

        // 按钮绑定
        addBtn.addActionListener(e -> addRanking());
        deleteBtn.addActionListener(e -> deleteRanking());
        refreshBtn.addActionListener(e -> loadData());
        backBtn.addActionListener(e -> {
            this.dispose();
            new MainDashboard(currentTeacher).setVisible(true);
        });

        panel.add(addBtn);
        panel.add(deleteBtn);
        panel.add(refreshBtn);
        panel.add(backBtn);

        add(panel, BorderLayout.NORTH);

        // 表格区域
        model = new DefaultTableModel(new String[]{"成果ID", "教师ID", "排名"}, 0);
        table = new JTable(model);
        table.addMouseListener(new MouseAdapter() {
            public void mouseClicked(MouseEvent e) {
                int row = table.getSelectedRow();
                achievementIdField.setText(model.getValueAt(row, 0).toString());
                teacherIdField.setText(model.getValueAt(row, 1).toString());
                rankingField.setText(model.getValueAt(row, 2).toString());
            }
        });

        add(new JScrollPane(table), BorderLayout.CENTER);
        loadData();
    }

    private void loadData() {
        model.setRowCount(0);
        String sql = "SELECT * FROM AchievementRanking ORDER BY achievement_id, ranking";
        try (Connection conn = DBUtil.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {
            while (rs.next()) {
                model.addRow(new Object[]{
                        rs.getInt("achievement_id"),
                        rs.getInt("teacher_id"),
                        rs.getInt("ranking")
                });
            }
        } catch (SQLException e) {
            showError("加载失败: " + e.getMessage());
        }
    }

    private void addRanking() {
        String sql = "INSERT INTO AchievementRanking (achievement_id, teacher_id, ranking) VALUES (?, ?, ?)";
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setInt(1, Integer.parseInt(achievementIdField.getText()));
            stmt.setInt(2, Integer.parseInt(teacherIdField.getText()));
            stmt.setInt(3, Integer.parseInt(rankingField.getText()));
            stmt.executeUpdate();
            loadData();
        } catch (SQLException e) {
            showError("添加失败: " + e.getMessage());
        }
    }

    private void deleteRanking() {
        String sql = "DELETE FROM AchievementRanking WHERE achievement_id=? AND teacher_id=?";
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setInt(1, Integer.parseInt(achievementIdField.getText()));
            stmt.setInt(2, Integer.parseInt(teacherIdField.getText()));
            stmt.executeUpdate();
            loadData();
        } catch (SQLException e) {
            showError("删除失败: " + e.getMessage());
        }
    }

    private void showError(String msg) {
        JOptionPane.showMessageDialog(this, msg, "错误", JOptionPane.ERROR_MESSAGE);
    }
}
