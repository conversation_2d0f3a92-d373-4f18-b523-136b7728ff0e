import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

public class TeacherManager extends JFrame {

    Teacher currentTeacher;

    private JTable table;
    private DefaultTableModel model;
    private JTextField idField, nameField, genderField, deptField;
    private JTextField titleField, baseSalaryField, postSalaryField, bonusSalaryField, searchTitleField;

    public TeacherManager(Teacher teacher) {
        currentTeacher = teacher;
        setTitle("教师信息管理");
        setSize(1000, 500);
        setLocationRelativeTo(null);

        model = new DefaultTableModel(new String[]{"ID", "姓名", "性别", "部门ID", "职称", "基本工资", "岗位工资", "奖金"}, 0);
        table = new JTable(model);
        JScrollPane scrollPane = new JScrollPane(table);

        JPanel inputPanel = new JPanel(new GridLayout(2, 8));
        idField = new JTextField();
        nameField = new JTextField();
        genderField = new JTextField();
        deptField = new JTextField();
        titleField = new JTextField();
        baseSalaryField = new JTextField();
        postSalaryField = new JTextField();
        bonusSalaryField = new JTextField();

        inputPanel.add(new JLabel("ID:"));
        inputPanel.add(idField);
        inputPanel.add(new JLabel("姓名:"));
        inputPanel.add(nameField);
        inputPanel.add(new JLabel("性别:"));
        inputPanel.add(genderField);
        inputPanel.add(new JLabel("部门ID:"));
        inputPanel.add(deptField);

        inputPanel.add(new JLabel("职称:"));
        inputPanel.add(titleField);
        inputPanel.add(new JLabel("基本工资:"));
        inputPanel.add(baseSalaryField);
        inputPanel.add(new JLabel("岗位工资:"));
        inputPanel.add(postSalaryField);
        inputPanel.add(new JLabel("奖金:"));
        inputPanel.add(bonusSalaryField);

        JPanel btnPanel = new JPanel();
        JButton addBtn = new JButton("新增");
        JButton updateBtn = new JButton("修改");
        JButton deleteBtn = new JButton("删除");
        JButton refreshBtn = new JButton("刷新");
        JButton backBtn = new JButton("返回");
        searchTitleField = new JTextField(10);
        JButton searchBtn = new JButton("按职称查询");

        btnPanel.add(addBtn);
        btnPanel.add(updateBtn);
        btnPanel.add(deleteBtn);
        btnPanel.add(refreshBtn);
        btnPanel.add(backBtn);
        btnPanel.add(new JLabel("职称:"));
        btnPanel.add(searchTitleField);
        btnPanel.add(searchBtn);

        add(scrollPane, BorderLayout.CENTER);
        add(inputPanel, BorderLayout.NORTH);
        add(btnPanel, BorderLayout.SOUTH);

        refreshTable();

        addBtn.addActionListener(e -> addTeacher());
        updateBtn.addActionListener(e -> updateTeacher());
        deleteBtn.addActionListener(e -> deleteTeacher());
        refreshBtn.addActionListener(e -> refreshTable());
        backBtn.addActionListener(e -> back());
        searchBtn.addActionListener(e -> searchByTitle());

        table.addMouseListener(new MouseAdapter() {
            public void mouseClicked(MouseEvent e) {
                int r = table.getSelectedRow();
                if (r >= 0) {
                    idField.setText(model.getValueAt(r, 0).toString());
                    nameField.setText(model.getValueAt(r, 1).toString());
                    genderField.setText(model.getValueAt(r, 2).toString());
                    deptField.setText(model.getValueAt(r, 3).toString());
                    titleField.setText(model.getValueAt(r, 4).toString());
                    baseSalaryField.setText(model.getValueAt(r, 5).toString());
                    postSalaryField.setText(model.getValueAt(r, 6).toString());
                    bonusSalaryField.setText(model.getValueAt(r, 7).toString());
                }
            }
        });

        setVisible(true);
    }

    private void refreshTable() {
        model.setRowCount(0);
        String sql = "SELECT teacher_id, name, gender, dept_id, title, base_salary, post_salary, bonus_salary FROM Teacher";
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement ps = conn.prepareStatement(sql);
             ResultSet rs = ps.executeQuery()) {
            while (rs.next()) {
                model.addRow(new Object[]{
                        rs.getInt("teacher_id"),
                        rs.getString("name"),
                        rs.getString("gender"),
                        rs.getInt("dept_id"),
                        rs.getString("title"),
                        rs.getDouble("base_salary"),
                        rs.getDouble("post_salary"),
                        rs.getDouble("bonus_salary")
                });
            }
        } catch (SQLException e) {
            JOptionPane.showMessageDialog(this, "加载数据失败：" + e.getMessage());
        }
    }

    private void addTeacher() {
        try {
            String idText = idField.getText().trim();
            String name = nameField.getText().trim();
            String gender = genderField.getText().trim();
            String deptText = deptField.getText().trim();
            String title = titleField.getText().trim();
            String baseSalaryText = baseSalaryField.getText().trim();
            String postSalaryText = postSalaryField.getText().trim();
            String bonusSalaryText = bonusSalaryField.getText().trim();

            if (idText.isEmpty() || name.isEmpty() || gender.isEmpty() || deptText.isEmpty()
                    || title.isEmpty() || baseSalaryText.isEmpty() || postSalaryText.isEmpty() || bonusSalaryText.isEmpty()) {
                JOptionPane.showMessageDialog(this, "请填写所有字段！");
                return;
            }

            int id = Integer.parseInt(idText);
            int dept = Integer.parseInt(deptText);
            double baseSalary = Double.parseDouble(baseSalaryText);
            double postSalary = Double.parseDouble(postSalaryText);
            double bonusSalary = Double.parseDouble(bonusSalaryText);

            String sql = "INSERT INTO Teacher (teacher_id, name, gender, dept_id, title, base_salary, post_salary, bonus_salary) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
            try (Connection conn = DBUtil.getConnection();
                 PreparedStatement ps = conn.prepareStatement(sql)) {
                ps.setInt(1, id);
                ps.setString(2, name);
                ps.setString(3, gender);
                ps.setInt(4, dept);
                ps.setString(5, title);
                ps.setDouble(6, baseSalary);
                ps.setDouble(7, postSalary);
                ps.setDouble(8, bonusSalary);
                ps.executeUpdate();
                JOptionPane.showMessageDialog(this, "新增成功");
                refreshTable();
            }
        } catch (Exception e) {
            JOptionPane.showMessageDialog(this, "新增失败: " + e.getMessage());
        }
    }

    private void updateTeacher() {
        try {
            int id = Integer.parseInt(idField.getText());
            String name = nameField.getText();
            String gender = genderField.getText();
            int dept = Integer.parseInt(deptField.getText());
            String title = titleField.getText();
            double baseSalary = Double.parseDouble(baseSalaryField.getText());
            double postSalary = Double.parseDouble(postSalaryField.getText());
            double bonusSalary = Double.parseDouble(bonusSalaryField.getText());

            String sql = "UPDATE Teacher SET name=?, gender=?, dept_id=?, title=?, base_salary=?, post_salary=?, bonus_salary=? WHERE teacher_id=?";
            try (Connection conn = DBUtil.getConnection();
                 PreparedStatement ps = conn.prepareStatement(sql)) {
                ps.setString(1, name);
                ps.setString(2, gender);
                ps.setInt(3, dept);
                ps.setString(4, title);
                ps.setDouble(5, baseSalary);
                ps.setDouble(6, postSalary);
                ps.setDouble(7, bonusSalary);
                ps.setInt(8, id);
                int updated = ps.executeUpdate();
                if (updated > 0) {
                    JOptionPane.showMessageDialog(this, "修改成功");
                } else {
                    JOptionPane.showMessageDialog(this, "找不到该教师ID");
                }
                refreshTable();
            }
        } catch (Exception e) {
            JOptionPane.showMessageDialog(this, "修改失败: " + e.getMessage());
        }
    }

    private void deleteTeacher() {
        try {
            int id = Integer.parseInt(idField.getText());

            String sql = "DELETE FROM Teacher WHERE teacher_id=?";
            try (Connection conn = DBUtil.getConnection();
                 PreparedStatement ps = conn.prepareStatement(sql)) {
                ps.setInt(1, id);
                int deleted = ps.executeUpdate();
                if (deleted > 0) {
                    JOptionPane.showMessageDialog(this, "删除成功");
                } else {
                    JOptionPane.showMessageDialog(this, "找不到该教师ID");
                }
                refreshTable();
            }
        } catch (Exception e) {
            JOptionPane.showMessageDialog(this, "删除失败: " + e.getMessage());
        }
    }

    private void searchByTitle() {
        String title = searchTitleField.getText().trim();
        if (title.isEmpty()) {
            JOptionPane.showMessageDialog(this, "请输入职称再搜索");
            return;
        }
        model.setRowCount(0);
        String sql = "SELECT teacher_id, name, gender, dept_id, title, base_salary, post_salary, bonus_salary FROM Teacher WHERE title = ?";
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement ps = conn.prepareStatement(sql)) {
            ps.setString(1, title);
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    model.addRow(new Object[]{
                            rs.getInt("teacher_id"),
                            rs.getString("name"),
                            rs.getString("gender"),
                            rs.getInt("dept_id"),
                            rs.getString("title"),
                            rs.getDouble("base_salary"),
                            rs.getDouble("post_salary"),
                            rs.getDouble("bonus_salary")
                    });
                }
            }
        } catch (SQLException e) {
            JOptionPane.showMessageDialog(this, "查询失败: " + e.getMessage());
        }
    }

    private void back() {
        this.dispose();
        new MainDashboard(currentTeacher);
    }
}
