import javax.swing.*;
import java.awt.*;

public class MainDashboard extends J<PERSON>rame {
    Teacher currentTeacher;

    public MainDashboard(Teacher teacher) {
        this.currentTeacher = teacher;

        setTitle("科研管理系统 - 主界面（欢迎 " + currentTeacher.getName() + "）");
        setSize(600, 400);
        setLocationRelativeTo(null);
        setDefaultCloseOperation(EXIT_ON_CLOSE);
        setLayout(new GridLayout(3, 2, 20, 20));

        // 六个按钮
        JButton teacherBtn = new JButton("教师信息管理");
        JButton projectBtn = new JButton("项目与参与教师管理");
        JButton achievementBtn = new JButton("科研成果与排名管理");
        JButton queryByTeacherBtn = new JButton("查询某教师成果与项目");
        JButton queryByDeptBtn = new JButton("查询某部门成果与经费");
        JButton logoutBtn = new JButton("退出系统");

        // 添加按钮到布局
        add(teacherBtn);
        add(projectBtn);
        add(achievementBtn);
        add(queryByTeacherBtn);
        add(queryByDeptBtn);
        add(logoutBtn);

        // 教师信息管理
        teacherBtn.addActionListener(e -> {
            new TeacherManager(currentTeacher).setVisible(true);
            MainDashboard.this.dispose();
        });

        // 项目与参与教师管理
        projectBtn.addActionListener(e -> {
            new ProjectParticipationManager(currentTeacher).setVisible(true);
            MainDashboard.this.dispose();
        });

        // 成果与排名管理
        achievementBtn.addActionListener(e -> {
            new AchievementManager(currentTeacher).setVisible(true);
            MainDashboard.this.dispose();
        });

        // 教师参与项目与成果查询
        queryByTeacherBtn.addActionListener(e -> {
            new TeacherProjectResultViewer(currentTeacher).setVisible(true);
            MainDashboard.this.dispose();
        });

        // 查询部门成果与经费
        queryByDeptBtn.addActionListener(e -> {
            new DepartmentAchievementViewer(currentTeacher).setVisible(true);
            MainDashboard.this.dispose();
        });

        // 退出系统
        logoutBtn.addActionListener(e -> {
            MainDashboard.this.dispose();
            new LoginFrame().setVisible(true);
        });

        // 显示主界面
        this.setVisible(true);
    }
}
