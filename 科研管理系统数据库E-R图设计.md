# 科研管理系统数据库E-R图设计

## 1. 概念模型设计

### 1.1 传统E-R图概览

```
                    ┌─────────────┐
                    │    部门     │
                    │ DEPARTMENT  │
                    └──────┬──────┘
                           │
                           │ 1:N (包含)
                           │
                    ┌──────▼──────┐
                    │    教师     │
                    │  TEACHER    │
                    └──────┬──────┘
                           │
                    ┌──────┴──────┐
                    │             │
                    │ 1:N (负责)  │ M:N (参与)
                    │             │
            ┌───────▼──────┐     ┌▼─────────────┐
            │     项目     │     │   项目参与   │
            │   PROJECT    │     │PARTICIPATION │
            └───────┬──────┘     └──────────────┘
                    │
                    │ 1:N (产生)
                    │
            ┌───────▼──────┐
            │     成果     │     ┌──────────────┐
            │ ACHIEVEMENT  │────▶│   成果排名   │
            └──────────────┘ M:N │   RANKING    │
                                 └──────────────┘
```

### 1.2 详细E-R图（Chen表示法）

```
    ┌─────────┐                    ┌─────────┐
    │  部门   │                    │  教师   │
    │DEPARTMENT│                   │ TEACHER │
    └────┬────┘                    └────┬────┘
         │                              │
    dept_id(PK)                   teacher_id(PK)
    dept_name                     username
    dept_code                     password
    description                   name
                                  gender
         │                        birth_date
         │                        ethnicity
         │ 包含                   education
         │ 1:N                    title
         │                        base_salary
         ▼                        post_salary
    ┌─────────┐                   bonus_salary
    │         │                   dept_id(FK)
    │         │                        │
    │         │                        │
    │         │                        │ 负责
    │         │                        │ 1:N
    │         │                        ▼
    │         │                   ┌─────────┐
    │         │                   │  项目   │
    │         │                   │ PROJECT │
    │         │                   └────┬────┘
    │         │                        │
    │         │                   project_id(PK)
    │         │                   project_name
    │         │                   project_type
    │         │                   manager_id(FK)
    │         │                   fund
    │         │                   start_date
    │         │                   end_date
    │         │                   status
    │         │                        │
    │         │                        │ 产生
    │         │                        │ 1:N
    │         │                        ▼
    │         │                   ┌─────────┐
    │         │                   │  成果   │
    │         │                   │ACHIEVEMENT│
    │         │                   └────┬────┘
    │         │                        │
    │         │                   achievement_id(PK)
    │         │                   project_id(FK)
    │         │                   title
    │         │                   category
    │         │                   level
    │         │                   grade
    │         │                   approval_date
    │         │                        │
    │         │                        │
    │         │    ┌─────────────────────┘
    │         │    │
    │         │    │ 参与(M:N)
    │         │    │
    │         │    ▼
    │         │ ┌─────────┐         ┌─────────┐
    │         │ │项目参与 │         │成果排名 │
    │         │ │PARTICIPATION│     │ RANKING │
    │         │ └─────────┘         └─────────┘
    │         │      │                   │
    │         │ teacher_id(FK)      achievement_id(FK)
    │         │ project_id(FK)      teacher_id(FK)
    │         │ role                ranking
    │         │ contribution_rate   author_type
    │         │ join_date          contribution_rate
    │         │                    confirm_date
    │         │                         │
    │         └─────────────────────────┘
                     贡献(M:N)
```

### 1.3 E-R图符号说明

在传统的E-R图中，我们使用以下符号：

- **矩形** □：表示实体（Entity）
- **菱形** ◇：表示关系（Relationship）
- **椭圆** ○：表示属性（Attribute）
- **下划线**：表示主键属性
- **虚线椭圆**：表示派生属性
- **双线椭圆**：表示多值属性
- **连接线**：表示实体与关系、关系与属性之间的联系
- **基数标记**：1、N、M 表示关系的基数

### 1.4 基数约束说明

- **1:1**（一对一）：一个实体实例只能与另一个实体的一个实例相关联
- **1:N**（一对多）：一个实体实例可以与另一个实体的多个实例相关联
- **M:N**（多对多）：一个实体的多个实例可以与另一个实体的多个实例相关联

## 2. 实体详细设计

### 2.1 实体集合（Entity Sets）

#### 2.1.1 部门实体 (DEPARTMENT)

**实体描述：** 表示学校的各个部门组织结构

**主要属性：**
- **dept_id**（主键）：部门唯一标识符
- **dept_name**：部门名称
- **dept_code**：部门编码
- **description**：部门描述

**属性详细说明：**

| 属性名 | 属性类型 | 约束 | 描述 |
|--------|----------|------|------|
| dept_id | 简单属性 | 主键 | 部门唯一标识符 |
| dept_name | 简单属性 | 非空，唯一 | 部门名称 |
| dept_code | 简单属性 | 唯一 | 部门编码 |
| description | 简单属性 | 可空 | 部门描述信息 |

#### 2.1.2 教师实体 (TEACHER)

**实体描述：** 表示学校的教师，包含个人信息和工作信息

**主要属性：**
- **teacher_id**（主键）：教师唯一标识符
- **username**：登录用户名
- **name**：教师姓名
- **gender**：性别
- **birth_date**：出生日期
- **ethnicity**：民族
- **education**：学历
- **title**：职称
- **base_salary**：基本工资
- **post_salary**：岗位工资
- **bonus_salary**：奖金
- **dept_id**（外键）：所属部门

**属性详细说明：**

| 属性名 | 属性类型 | 约束 | 描述 |
|--------|----------|------|------|
| teacher_id | 简单属性 | 主键 | 教师唯一标识符 |
| username | 简单属性 | 非空，唯一 | 登录用户名 |
| name | 简单属性 | 非空 | 教师姓名 |
| gender | 简单属性 | 可空 | 性别（男/女/其他） |
| birth_date | 简单属性 | 可空 | 出生日期 |
| ethnicity | 简单属性 | 可空 | 民族 |
| education | 简单属性 | 可空 | 最高学历 |
| title | 简单属性 | 可空 | 职称 |
| base_salary | 简单属性 | 可空 | 基本工资 |
| post_salary | 简单属性 | 可空 | 岗位工资 |
| bonus_salary | 简单属性 | 可空 | 绩效奖金 |
| dept_id | 简单属性 | 外键 | 所属部门ID |

#### 2.1.3 项目实体 (PROJECT)

**实体描述：** 表示科研项目，包含项目的基本信息和管理数据

**主要属性：**
- **project_id**（主键）：项目唯一标识符
- **project_name**：项目名称
- **project_type**：项目类型
- **manager_id**（外键）：项目负责人ID
- **fund**：项目经费
- **start_date**：开始日期
- **end_date**：结束日期
- **status**：项目状态

**属性详细说明：**

| 属性名 | 属性类型 | 约束 | 描述 |
|--------|----------|------|------|
| project_id | 简单属性 | 主键 | 项目唯一标识符 |
| project_name | 简单属性 | 非空 | 项目名称 |
| project_type | 简单属性 | 可空 | 项目类型 |
| manager_id | 简单属性 | 外键 | 项目负责人ID |
| fund | 简单属性 | 可空 | 项目经费 |
| start_date | 简单属性 | 可空 | 项目开始日期 |
| end_date | 简单属性 | 可空 | 项目结束日期 |
| status | 简单属性 | 可空 | 项目状态 |

#### 2.1.4 成果实体 (ACHIEVEMENT)

**实体描述：** 表示科研成果，包含成果的基本信息和评价数据

**主要属性：**
- **achievement_id**（主键）：成果唯一标识符
- **project_id**（外键）：关联项目ID
- **title**：成果标题
- **category**：成果类别
- **level**：成果级别
- **grade**：成果等级
- **approval_date**：批准日期

**属性详细说明：**

| 属性名 | 属性类型 | 约束 | 描述 |
|--------|----------|------|------|
| achievement_id | 简单属性 | 主键 | 成果唯一标识符 |
| project_id | 简单属性 | 外键 | 关联项目ID |
| title | 简单属性 | 非空 | 成果标题 |
| category | 简单属性 | 非空 | 成果类别 |
| level | 简单属性 | 可空 | 成果级别 |
| grade | 简单属性 | 可空 | 成果等级 |
| approval_date | 简单属性 | 可空 | 批准日期 |

### 2.2 关系集合（Relationship Sets）

#### 2.2.1 包含关系 (BELONG)

**关系描述：** 部门与教师之间的一对多关系

- **参与实体：** 部门（DEPARTMENT）、教师（TEACHER）
- **关系类型：** 1:N（一个部门包含多名教师，一名教师属于一个部门）
- **关系属性：** 无

#### 2.2.2 负责关系 (MANAGE)

**关系描述：** 教师与项目之间的一对多管理关系

- **参与实体：** 教师（TEACHER）、项目（PROJECT）
- **关系类型：** 1:N（一名教师可以负责多个项目，一个项目只有一名负责人）
- **关系属性：** 无

#### 2.2.3 参与关系 (PARTICIPATE)

**关系描述：** 教师与项目之间的多对多参与关系

- **参与实体：** 教师（TEACHER）、项目（PROJECT）
- **关系类型：** M:N（一名教师可以参与多个项目，一个项目可以有多名教师参与）
- **关系属性：**
  - **role**：参与角色
  - **contribution_rate**：贡献比例
  - **join_date**：加入日期

#### 2.2.4 产生关系 (PRODUCE)

**关系描述：** 项目与成果之间的一对多产生关系

- **参与实体：** 项目（PROJECT）、成果（ACHIEVEMENT）
- **关系类型：** 1:N（一个项目可以产生多个成果，一个成果属于一个项目）
- **关系属性：** 无

#### 2.2.5 贡献关系 (CONTRIBUTE)

**关系描述：** 教师与成果之间的多对多贡献关系

- **参与实体：** 教师（TEACHER）、成果（ACHIEVEMENT）
- **关系类型：** M:N（一名教师可以贡献多个成果，一个成果可以有多名教师贡献）
- **关系属性：**
  - **ranking**：排名
  - **author_type**：作者类型
  - **contribution_rate**：贡献比例

## 3. 关系实体设计

### 3.1 项目参与关系 (PROJECT_PARTICIPATION)

**关系描述：** 教师与项目之间的多对多参与关系

| 属性名 | 数据类型 | 约束条件 | 描述 |
|--------|----------|----------|------|
| teacher_id | BIGINT | FOREIGN KEY | 教师ID |
| project_id | INT | FOREIGN KEY | 项目ID |
| role | VARCHAR(100) | | 参与角色 |
| responsibility | TEXT | | 职责描述 |
| contribution_rate | DECIMAL(5,2) | CHECK (contribution_rate BETWEEN 0 AND 100) | 贡献比例(%) |
| workload | DECIMAL(8,2) | | 工作量(人月) |
| join_date | DATE | | 加入日期 |
| leave_date | DATE | | 离开日期 |
| status | ENUM('参与中','已完成','已退出') | DEFAULT '参与中' | 参与状态 |
| performance_rating | VARCHAR(20) | | 绩效评级 |
| remarks | TEXT | | 备注 |
| create_time | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| update_time | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

**主键：** (teacher_id, project_id)

### 3.2 成果排名关系 (ACHIEVEMENT_RANKING)

**关系描述：** 教师在成果中的排名和贡献关系

| 属性名 | 数据类型 | 约束条件 | 描述 |
|--------|----------|----------|------|
| achievement_id | INT | FOREIGN KEY | 成果ID |
| teacher_id | BIGINT | FOREIGN KEY | 教师ID |
| ranking | INT | NOT NULL | 排名顺序 |
| author_type | ENUM('第一作者','通讯作者','共同第一作者','共同通讯作者','其他作者') | | 作者类型 |
| contribution_rate | DECIMAL(5,2) | CHECK (contribution_rate BETWEEN 0 AND 100) | 贡献比例(%) |
| contribution_description | TEXT | | 贡献描述 |
| signature_unit | VARCHAR(200) | | 署名单位 |
| is_corresponding | BOOLEAN | DEFAULT FALSE | 是否通讯作者 |
| confirm_date | DATE | | 确认日期 |
| confirm_status | ENUM('待确认','已确认','有争议') | DEFAULT '待确认' | 确认状态 |
| remarks | TEXT | | 备注 |
| create_time | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| update_time | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

**主键：** (achievement_id, teacher_id)

## 3. E-R图完整性约束

### 3.1 实体完整性约束

每个实体集合都必须有一个主键来唯一标识实体实例：

- **部门实体**：dept_id 作为主键
- **教师实体**：teacher_id 作为主键
- **项目实体**：project_id 作为主键
- **成果实体**：achievement_id 作为主键

### 3.2 参照完整性约束

外键约束确保关系的一致性：

- **教师.dept_id** 必须引用存在的 **部门.dept_id**
- **项目.manager_id** 必须引用存在的 **教师.teacher_id**
- **成果.project_id** 必须引用存在的 **项目.project_id**

### 3.3 基数约束

关系的基数约束限制了实体间的关联数量：

1. **包含关系（部门-教师）**：1:N
   - 每个部门可以包含0到多名教师
   - 每名教师必须属于且仅属于1个部门

2. **负责关系（教师-项目）**：1:N
   - 每名教师可以负责0到多个项目
   - 每个项目必须有且仅有1名负责人

3. **参与关系（教师-项目）**：M:N
   - 每名教师可以参与0到多个项目
   - 每个项目可以有0到多名教师参与

4. **产生关系（项目-成果）**：1:N
   - 每个项目可以产生0到多个成果
   - 每个成果必须属于且仅属于1个项目

5. **贡献关系（教师-成果）**：M:N
   - 每名教师可以贡献0到多个成果
   - 每个成果可以有1到多名教师贡献

### 3.4 域约束

属性值必须符合定义的域：

- **性别**：只能是'男'、'女'、'其他'
- **项目状态**：只能是'申报中'、'立项'、'执行中'、'结题'、'终止'
- **成果等级**：必须是正整数
- **贡献比例**：必须在0-100之间
- **日期**：必须是有效的日期格式

## 4. E-R图转换为关系模式

### 4.1 实体转换规则

每个实体集合转换为一个关系模式：

- **部门** → DEPARTMENT(dept_id, dept_name, dept_code, description)
- **教师** → TEACHER(teacher_id, username, name, gender, birth_date, ethnicity, education, title, base_salary, post_salary, bonus_salary, dept_id)
- **项目** → PROJECT(project_id, project_name, project_type, manager_id, fund, start_date, end_date, status)
- **成果** → ACHIEVEMENT(achievement_id, project_id, title, category, level, grade, approval_date)

### 4.2 关系转换规则

#### 4.2.1 一对多关系转换

一对多关系通过在"多"端添加外键实现：

- **包含关系**：在TEACHER表中添加dept_id外键
- **负责关系**：在PROJECT表中添加manager_id外键
- **产生关系**：在ACHIEVEMENT表中添加project_id外键

#### 4.2.2 多对多关系转换

多对多关系需要创建独立的关系表：

- **参与关系** → PROJECT_PARTICIPATION(teacher_id, project_id, role, contribution_rate, join_date)
- **贡献关系** → ACHIEVEMENT_RANKING(achievement_id, teacher_id, ranking, author_type, contribution_rate)

## 5. 数据完整性约束

### 5.1 实体完整性
- 每个实体都有唯一的主键标识
- 主键不能为空且唯一

### 5.2 参照完整性
- 所有外键都必须引用存在的主键值
- 级联更新和删除策略

### 5.3 用户定义完整性
- 日期逻辑约束：结束日期不能早于开始日期
- 数值范围约束：贡献比例在0-100之间
- 枚举值约束：状态字段只能取预定义值
- 唯一性约束：用户名、身份证号等唯一

### 5.4 域完整性
- 数据类型约束
- 长度限制
- 格式验证（如邮箱格式、电话格式）

## 6. 索引设计建议

### 6.1 主键索引
- 所有主键自动创建聚集索引

### 6.2 外键索引
```sql
-- 教师表索引
CREATE INDEX idx_teacher_dept ON teacher(dept_id);
CREATE INDEX idx_teacher_username ON teacher(username);
CREATE INDEX idx_teacher_name ON teacher(name);
CREATE INDEX idx_teacher_title ON teacher(title);

-- 项目表索引
CREATE INDEX idx_project_manager ON project(manager_id);
CREATE INDEX idx_project_name ON project(project_name);
CREATE INDEX idx_project_type ON project(project_type);
CREATE INDEX idx_project_status ON project(status);

-- 成果表索引
CREATE INDEX idx_achievement_project ON achievement(project_id);
CREATE INDEX idx_achievement_category ON achievement(category);
CREATE INDEX idx_achievement_level ON achievement(level);
CREATE INDEX idx_achievement_date ON achievement(publication_date);

-- 关系表索引
CREATE INDEX idx_participation_teacher ON project_participation(teacher_id);
CREATE INDEX idx_participation_project ON project_participation(project_id);
CREATE INDEX idx_ranking_teacher ON achievement_ranking(teacher_id);
CREATE INDEX idx_ranking_achievement ON achievement_ranking(achievement_id);
```

### 6.3 复合索引
```sql
-- 常用查询组合索引
CREATE INDEX idx_teacher_dept_title ON teacher(dept_id, title);
CREATE INDEX idx_project_manager_status ON project(manager_id, status);
CREATE INDEX idx_achievement_project_category ON achievement(project_id, category);
```

## 7. 视图设计建议

### 7.1 教师项目成果统计视图
```sql
CREATE VIEW v_teacher_statistics AS
SELECT 
    t.teacher_id,
    t.name,
    t.dept_id,
    COUNT(DISTINCT pp.project_id) as project_count,
    COUNT(DISTINCT ar.achievement_id) as achievement_count,
    SUM(p.total_fund * pp.contribution_rate / 100) as total_funding
FROM teacher t
LEFT JOIN project_participation pp ON t.teacher_id = pp.teacher_id
LEFT JOIN project p ON pp.project_id = p.project_id
LEFT JOIN achievement_ranking ar ON t.teacher_id = ar.teacher_id
GROUP BY t.teacher_id, t.name, t.dept_id;
```

### 7.2 部门科研统计视图
```sql
CREATE VIEW v_department_statistics AS
SELECT 
    d.dept_id,
    d.dept_name,
    COUNT(DISTINCT t.teacher_id) as teacher_count,
    COUNT(DISTINCT p.project_id) as project_count,
    COUNT(DISTINCT a.achievement_id) as achievement_count,
    SUM(p.total_fund) as total_funding
FROM department d
LEFT JOIN teacher t ON d.dept_id = t.dept_id
LEFT JOIN project p ON t.teacher_id = p.manager_id
LEFT JOIN achievement a ON p.project_id = a.project_id
GROUP BY d.dept_id, d.dept_name;
```

## 8. 数据库建表SQL语句

### 8.1 创建数据库
```sql
-- 创建数据库
CREATE DATABASE research_management_system
CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci;

USE research_management_system;
```

### 8.2 创建表结构

#### 8.2.1 部门表
```sql
CREATE TABLE department (
    dept_id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '部门ID',
    dept_name VARCHAR(100) NOT NULL UNIQUE COMMENT '部门名称',
    dept_code VARCHAR(20) UNIQUE COMMENT '部门编码',
    description TEXT COMMENT '部门描述',
    create_date DATE COMMENT '成立日期',
    parent_dept_id BIGINT COMMENT '上级部门ID',
    dept_level INT DEFAULT 1 COMMENT '部门层级',
    contact_phone VARCHAR(20) COMMENT '联系电话',
    contact_email VARCHAR(100) COMMENT '联系邮箱',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (parent_dept_id) REFERENCES department(dept_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='部门信息表';
```

#### 8.2.2 教师表
```sql
CREATE TABLE teacher (
    teacher_id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '教师ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码',
    name VARCHAR(100) NOT NULL COMMENT '姓名',
    gender ENUM('男','女','其他') COMMENT '性别',
    birth_date DATE COMMENT '出生日期',
    id_card VARCHAR(18) UNIQUE COMMENT '身份证号',
    ethnicity VARCHAR(50) COMMENT '民族',
    education VARCHAR(100) COMMENT '最高学历',
    degree VARCHAR(50) COMMENT '最高学位',
    graduate_school VARCHAR(200) COMMENT '毕业院校',
    major VARCHAR(100) COMMENT '专业方向',
    start_work_date DATE COMMENT '参加工作日期',
    join_date DATE COMMENT '入职日期',
    title VARCHAR(100) COMMENT '职称',
    title_date DATE COMMENT '职称获得日期',
    research_direction TEXT COMMENT '研究方向',
    base_salary DECIMAL(10,2) COMMENT '基本工资',
    post_salary DECIMAL(10,2) COMMENT '岗位工资',
    bonus_salary DECIMAL(10,2) COMMENT '绩效奖金',
    dept_id BIGINT COMMENT '所属部门ID',
    phone VARCHAR(20) COMMENT '联系电话',
    email VARCHAR(100) COMMENT '邮箱地址',
    office_location VARCHAR(200) COMMENT '办公地点',
    status ENUM('在职','离职','退休') DEFAULT '在职' COMMENT '工作状态',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (dept_id) REFERENCES department(dept_id),
    INDEX idx_teacher_dept (dept_id),
    INDEX idx_teacher_username (username),
    INDEX idx_teacher_name (name),
    INDEX idx_teacher_title (title)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='教师信息表';
```

#### 8.2.3 项目表
```sql
CREATE TABLE project (
    project_id INT AUTO_INCREMENT PRIMARY KEY COMMENT '项目ID',
    project_code VARCHAR(50) UNIQUE COMMENT '项目编号',
    project_name VARCHAR(500) NOT NULL COMMENT '项目名称',
    project_type VARCHAR(100) COMMENT '项目类型',
    project_level VARCHAR(50) COMMENT '项目级别',
    funding_agency VARCHAR(200) COMMENT '资助机构',
    manager_id BIGINT COMMENT '项目负责人ID',
    total_fund DECIMAL(15,2) COMMENT '项目总经费',
    approved_fund DECIMAL(15,2) COMMENT '批准经费',
    start_date DATE COMMENT '项目开始日期',
    end_date DATE COMMENT '项目结束日期',
    actual_start_date DATE COMMENT '实际开始日期',
    actual_end_date DATE COMMENT '实际结束日期',
    status ENUM('申报中','立项','执行中','结题','终止') DEFAULT '申报中' COMMENT '项目状态',
    research_field VARCHAR(200) COMMENT '研究领域',
    keywords TEXT COMMENT '关键词',
    abstract TEXT COMMENT '项目摘要',
    objectives TEXT COMMENT '项目目标',
    methodology TEXT COMMENT '研究方法',
    expected_results TEXT COMMENT '预期成果',
    risk_assessment TEXT COMMENT '风险评估',
    progress_report TEXT COMMENT '进展报告',
    final_report TEXT COMMENT '结题报告',
    evaluation_result VARCHAR(50) COMMENT '评估结果',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (manager_id) REFERENCES teacher(teacher_id),
    INDEX idx_project_manager (manager_id),
    INDEX idx_project_name (project_name),
    INDEX idx_project_type (project_type),
    INDEX idx_project_status (status),
    CONSTRAINT chk_project_dates CHECK (end_date >= start_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目信息表';
```

#### 8.2.4 成果表
```sql
CREATE TABLE achievement (
    achievement_id INT AUTO_INCREMENT PRIMARY KEY COMMENT '成果ID',
    project_id INT COMMENT '关联项目ID',
    title VARCHAR(500) NOT NULL COMMENT '成果标题',
    category VARCHAR(100) NOT NULL COMMENT '成果类别',
    sub_category VARCHAR(100) COMMENT '成果子类别',
    level VARCHAR(50) COMMENT '成果级别',
    grade INT COMMENT '成果等级',
    journal_name VARCHAR(300) COMMENT '期刊名称',
    publisher VARCHAR(200) COMMENT '出版社',
    publication_date DATE COMMENT '发表/出版日期',
    approval_date DATE COMMENT '批准日期',
    volume_issue VARCHAR(50) COMMENT '卷期号',
    page_numbers VARCHAR(50) COMMENT '页码',
    doi VARCHAR(100) COMMENT 'DOI号',
    isbn VARCHAR(20) COMMENT 'ISBN号',
    patent_number VARCHAR(50) COMMENT '专利号',
    award_name VARCHAR(200) COMMENT '奖项名称',
    award_level VARCHAR(50) COMMENT '奖项级别',
    award_organization VARCHAR(200) COMMENT '颁奖机构',
    impact_factor DECIMAL(8,3) COMMENT '影响因子',
    citation_count INT DEFAULT 0 COMMENT '被引次数',
    download_count INT DEFAULT 0 COMMENT '下载次数',
    h_index DECIMAL(8,3) COMMENT 'H指数',
    quality_score DECIMAL(5,2) COMMENT '质量评分',
    abstract TEXT COMMENT '成果摘要',
    keywords TEXT COMMENT '关键词',
    research_field VARCHAR(200) COMMENT '研究领域',
    language VARCHAR(50) DEFAULT '中文' COMMENT '语言',
    country VARCHAR(100) COMMENT '国家/地区',
    funding_info TEXT COMMENT '资助信息',
    collaboration_type VARCHAR(50) COMMENT '合作类型',
    open_access BOOLEAN DEFAULT FALSE COMMENT '是否开放获取',
    status ENUM('草稿','提交','发表','获奖','专利授权') DEFAULT '草稿' COMMENT '状态',
    file_path VARCHAR(500) COMMENT '文件路径',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (project_id) REFERENCES project(project_id),
    INDEX idx_achievement_project (project_id),
    INDEX idx_achievement_category (category),
    INDEX idx_achievement_level (level),
    INDEX idx_achievement_date (publication_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='成果信息表';
```

#### 8.2.5 项目参与表
```sql
CREATE TABLE project_participation (
    teacher_id BIGINT COMMENT '教师ID',
    project_id INT COMMENT '项目ID',
    role VARCHAR(100) COMMENT '参与角色',
    responsibility TEXT COMMENT '职责描述',
    contribution_rate DECIMAL(5,2) COMMENT '贡献比例(%)',
    workload DECIMAL(8,2) COMMENT '工作量(人月)',
    join_date DATE COMMENT '加入日期',
    leave_date DATE COMMENT '离开日期',
    status ENUM('参与中','已完成','已退出') DEFAULT '参与中' COMMENT '参与状态',
    performance_rating VARCHAR(20) COMMENT '绩效评级',
    remarks TEXT COMMENT '备注',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (teacher_id, project_id),
    FOREIGN KEY (teacher_id) REFERENCES teacher(teacher_id),
    FOREIGN KEY (project_id) REFERENCES project(project_id),
    INDEX idx_participation_teacher (teacher_id),
    INDEX idx_participation_project (project_id),
    CONSTRAINT chk_contribution_rate CHECK (contribution_rate BETWEEN 0 AND 100)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目参与表';
```

#### 8.2.6 成果排名表
```sql
CREATE TABLE achievement_ranking (
    achievement_id INT COMMENT '成果ID',
    teacher_id BIGINT COMMENT '教师ID',
    ranking INT NOT NULL COMMENT '排名顺序',
    author_type ENUM('第一作者','通讯作者','共同第一作者','共同通讯作者','其他作者') COMMENT '作者类型',
    contribution_rate DECIMAL(5,2) COMMENT '贡献比例(%)',
    contribution_description TEXT COMMENT '贡献描述',
    signature_unit VARCHAR(200) COMMENT '署名单位',
    is_corresponding BOOLEAN DEFAULT FALSE COMMENT '是否通讯作者',
    confirm_date DATE COMMENT '确认日期',
    confirm_status ENUM('待确认','已确认','有争议') DEFAULT '待确认' COMMENT '确认状态',
    remarks TEXT COMMENT '备注',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (achievement_id, teacher_id),
    FOREIGN KEY (achievement_id) REFERENCES achievement(achievement_id),
    FOREIGN KEY (teacher_id) REFERENCES teacher(teacher_id),
    INDEX idx_ranking_teacher (teacher_id),
    INDEX idx_ranking_achievement (achievement_id),
    CONSTRAINT chk_ranking_contribution CHECK (contribution_rate BETWEEN 0 AND 100)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='成果排名表';
```

### 8.3 创建视图

#### 8.3.1 教师统计视图
```sql
CREATE VIEW v_teacher_statistics AS
SELECT
    t.teacher_id,
    t.name,
    t.title,
    d.dept_name,
    COUNT(DISTINCT pp.project_id) as project_count,
    COUNT(DISTINCT ar.achievement_id) as achievement_count,
    COALESCE(SUM(p.total_fund * pp.contribution_rate / 100), 0) as total_funding,
    COALESCE(AVG(a.quality_score), 0) as avg_quality_score
FROM teacher t
LEFT JOIN department d ON t.dept_id = d.dept_id
LEFT JOIN project_participation pp ON t.teacher_id = pp.teacher_id
LEFT JOIN project p ON pp.project_id = p.project_id
LEFT JOIN achievement_ranking ar ON t.teacher_id = ar.teacher_id
LEFT JOIN achievement a ON ar.achievement_id = a.achievement_id
GROUP BY t.teacher_id, t.name, t.title, d.dept_name;
```

#### 8.3.2 部门统计视图
```sql
CREATE VIEW v_department_statistics AS
SELECT
    d.dept_id,
    d.dept_name,
    COUNT(DISTINCT t.teacher_id) as teacher_count,
    COUNT(DISTINCT p.project_id) as project_count,
    COUNT(DISTINCT a.achievement_id) as achievement_count,
    COALESCE(SUM(p.total_fund), 0) as total_funding,
    COALESCE(AVG(a.quality_score), 0) as avg_quality_score
FROM department d
LEFT JOIN teacher t ON d.dept_id = t.dept_id
LEFT JOIN project p ON t.teacher_id = p.manager_id
LEFT JOIN achievement a ON p.project_id = a.project_id
GROUP BY d.dept_id, d.dept_name;
```

### 8.4 插入示例数据

#### 8.4.1 部门数据
```sql
INSERT INTO department (dept_name, dept_code, description) VALUES
('计算机科学与技术学院', 'CS', '计算机科学与技术相关专业'),
('电子信息工程学院', 'EE', '电子信息工程相关专业'),
('机械工程学院', 'ME', '机械工程相关专业'),
('材料科学与工程学院', 'MSE', '材料科学与工程相关专业');
```

#### 8.4.2 教师数据
```sql
INSERT INTO teacher (username, password, name, gender, title, dept_id, base_salary, post_salary, bonus_salary) VALUES
('admin', SHA2('123456', 256), '张教授', '男', '教授', 1, 8000.00, 3000.00, 2000.00),
('teacher001', SHA2('123456', 256), '李副教授', '女', '副教授', 1, 7000.00, 2500.00, 1500.00),
('teacher002', SHA2('123456', 256), '王讲师', '男', '讲师', 2, 6000.00, 2000.00, 1000.00),
('teacher003', SHA2('123456', 256), '刘助教', '女', '助教', 3, 5000.00, 1500.00, 800.00);
```

## 9. E-R图设计总结

### 9.1 设计特点

这个传统E-R图设计具有以下特点：

1. **结构清晰**：采用经典的Chen表示法，实体用矩形表示，关系用菱形表示，属性用椭圆表示
2. **关系明确**：明确定义了实体间的1:1、1:N、M:N关系，并标注了基数约束
3. **属性完整**：每个实体都包含了必要的属性，主键用下划线标识
4. **约束严格**：定义了完整性约束、参照完整性约束和域约束
5. **转换规范**：提供了从E-R图到关系模式的标准转换方法

### 9.2 业务覆盖

该E-R图设计覆盖了科研管理系统的核心业务：

- **组织管理**：通过部门实体管理组织结构
- **人员管理**：通过教师实体管理人员信息
- **项目管理**：通过项目实体和相关关系管理科研项目
- **成果管理**：通过成果实体和相关关系管理科研成果
- **关联管理**：通过多对多关系管理复杂的业务关联

### 9.3 扩展性

设计具有良好的扩展性：

- **实体扩展**：可以方便地添加新的实体（如学生、课程等）
- **属性扩展**：可以为现有实体添加新的属性
- **关系扩展**：可以定义新的实体间关系
- **约束扩展**：可以添加更复杂的业务约束

### 9.4 实现建议

在实际实现时建议：

1. **严格遵循E-R图设计**：确保数据库实现与E-R图设计一致
2. **合理设置索引**：在外键和常用查询字段上建立索引
3. **定期维护约束**：确保完整性约束的有效性
4. **监控性能**：定期监控查询性能，必要时优化设计
5. **文档同步**：保持E-R图与实际数据库结构的同步

这个E-R图设计为科研管理系统提供了坚实的数据模型基础，支持复杂的科研管理业务需求，具有良好的扩展性和维护性。通过规范的设计方法和严格的约束定义，确保了数据的完整性、一致性和系统的可靠性。

## 10. 系统登录时序图

### 10.1 登录流程时序图

以下时序图展示了科研管理系统用户登录的完整流程：

```mermaid
sequenceDiagram
    participant U as 用户
    participant LI as 登录界面
    participant AS as 验证服务
    participant DB as 数据库
    participant MI as 主界面

    U->>LI: 1. 输入用户名和密码
    LI->>LI: 2. 前端数据验证
    LI->>AS: 3. 发送登录请求（加密）
    AS->>DB: 4. 查询用户信息
    DB->>AS: 5. 返回用户数据
    AS->>AS: 6. 密码验证和权限检查
    AS->>DB: 7. 记录登录日志
    AS->>LI: 8. 返回验证结果

    alt 登录成功
        LI->>MI: 9a. 跳转到主界面
        MI->>U: 10a. 显示系统主页
    else 登录失败
        LI->>U: 9b. 显示错误信息
    end
```

### 10.2 时序图说明

#### 10.2.1 参与者说明

| 参与者 | 描述 | 职责 |
|--------|------|------|
| 用户 | 系统使用者 | 输入登录凭据，接收系统反馈 |
| 登录界面 | 前端登录页面 | 收集用户输入，显示登录结果 |
| 验证服务 | 后端认证服务 | 处理登录逻辑，验证用户身份 |
| 数据库 | 数据存储层 | 存储用户信息，记录操作日志 |
| 主界面 | 系统主页面 | 显示系统功能，提供操作入口 |

#### 10.2.2 流程步骤详解

1. **用户输入**：用户在登录界面输入用户名和密码
2. **前端验证**：登录界面进行基本的格式验证（非空、长度等）
3. **请求发送**：登录界面将加密后的登录信息发送给验证服务
4. **用户查询**：验证服务向数据库查询用户信息
5. **数据返回**：数据库返回匹配的用户数据
6. **身份验证**：验证服务进行密码验证和权限检查
7. **日志记录**：验证服务向数据库记录登录尝试日志
8. **结果返回**：验证服务将验证结果返回给登录界面
9. **条件分支**：
   - **成功分支**：跳转到主界面，显示系统主页
   - **失败分支**：显示错误信息给用户

#### 10.2.3 安全考虑

- **密码加密**：用户密码在传输前进行客户端加密
- **HTTPS传输**：所有网络通信使用HTTPS协议
- **日志记录**：记录所有登录尝试，包括成功和失败的情况
- **权限检查**：验证用户状态和权限级别
- **错误处理**：统一的错误提示，不泄露敏感信息

#### 10.2.4 异常处理

- **网络异常**：超时重试机制
- **数据库异常**：连接池管理和故障转移
- **验证异常**：详细的错误分类和处理
- **并发控制**：防止重复登录和会话冲突

这个时序图清晰地展示了科研管理系统登录功能的交互流程，为系统设计和开发提供了重要的参考依据。

## 11. 教师信息管理功能模块设计

### 11.1 教师信息管理主流程图

```mermaid
flowchart TD
    A[开始] --> B[登录] --> C{成功?}
    C -->|否| D[错误] --> B
    C -->|是| E[管理界面] --> F{操作}

    F -->|查询| G[查询] --> H[结果] --> F

    F -->|增加| I[输入] --> J{验证}
    J -->|否| K[错误] --> I
    J -->|是| L[保存] --> M{成功?}
    M -->|否| N[失败] --> I
    M -->|是| O[刷新] --> F

    F -->|修改| P[编辑] --> Q{验证}
    Q -->|否| R[错误] --> P
    Q -->|是| S[更新] --> T{成功?}
    T -->|否| U[失败] --> P
    T -->|是| O

    F -->|删除| V{确认?}
    V -->|否| F
    V -->|是| W{关联?}
    W -->|是| X[警告] --> F
    W -->|否| Y[删除] --> Z{成功?}
    Z -->|否| AA[失败] --> F
    Z -->|是| O

    F -->|退出| BB[结束]

    style A fill:#e1f5fe
    style BB fill:#ffebee
    style C fill:#fff3e0
    style J fill:#fff3e0
    style Q fill:#fff3e0
    style V fill:#fff3e0
    style W fill:#fff3e0
```

### 11.2 添加教师信息时序图

```mermaid
sequenceDiagram
    participant U as 用户
    participant UI as 管理界面
    participant V as 数据验证器
    participant S as 业务服务
    participant DB as 数据库

    U->>UI: 1. 点击"添加教师"按钮
    UI->>UI: 2. 打开添加教师对话框
    U->>UI: 3. 输入教师基本信息
    U->>UI: 4. 点击"保存"按钮
    UI->>V: 5. 提交数据进行验证
    V->>V: 6. 执行数据格式验证

    alt 验证失败
        V->>UI: 7a. 返回验证错误信息
        UI->>U: 8a. 显示错误提示
    else 验证成功
        V->>S: 7b. 传递验证后的数据
        S->>DB: 8b. 检查用户名唯一性
        DB->>S: 9b. 返回检查结果

        alt 用户名已存在
            S->>UI: 10a. 返回用户名重复错误
            UI->>U: 11a. 显示重复错误提示
        else 用户名可用
            S->>DB: 10b. 插入教师记录
            DB->>S: 11b. 返回插入结果
            S->>UI: 12b. 返回操作结果
            UI->>UI: 13b. 关闭对话框
            UI->>UI: 14b. 刷新教师列表
            UI->>U: 15b. 显示成功提示
        end
    end
```

### 11.3 查询教师信息时序图

```mermaid
sequenceDiagram
    participant U as 用户
    participant UI as 管理界面
    participant S as 查询服务
    participant DB as 数据库

    U->>UI: 1. 设置查询条件
    U->>UI: 2. 点击"查询"按钮
    UI->>S: 3. 发送查询请求
    S->>S: 4. 构建SQL查询语句
    S->>DB: 5. 执行数据库查询
    DB->>S: 6. 返回查询结果
    S->>S: 7. 处理查询结果
    S->>UI: 8. 返回格式化数据
    UI->>UI: 9. 更新数据表格
    UI->>U: 10. 显示查询结果

    Note over U,DB: 支持多条件组合查询：<br/>姓名、职称、部门、入职时间等
```

### 11.4 修改教师信息时序图

```mermaid
sequenceDiagram
    participant U as 用户
    participant UI as 管理界面
    participant V as 数据验证器
    participant S as 业务服务
    participant DB as 数据库

    U->>UI: 1. 选择教师记录
    U->>UI: 2. 点击"编辑"按钮
    UI->>S: 3. 请求教师详细信息
    S->>DB: 4. 查询教师完整信息
    DB->>S: 5. 返回教师数据
    S->>UI: 6. 返回教师信息
    UI->>UI: 7. 打开编辑对话框
    UI->>U: 8. 显示当前教师信息

    U->>UI: 9. 修改教师信息
    U->>UI: 10. 点击"保存"按钮
    UI->>V: 11. 提交修改数据验证
    V->>V: 12. 执行数据验证

    alt 验证失败
        V->>UI: 13a. 返回验证错误
        UI->>U: 14a. 显示错误提示
    else 验证成功
        V->>S: 13b. 传递验证数据
        S->>DB: 14b. 更新教师记录
        DB->>S: 15b. 返回更新结果

        alt 更新失败
            S->>UI: 16a. 返回更新错误
            UI->>U: 17a. 显示错误提示
        else 更新成功
            S->>UI: 16b. 返回成功结果
            UI->>UI: 17b. 关闭编辑对话框
            UI->>UI: 18b. 刷新教师列表
            UI->>U: 19b. 显示成功提示
        end
    end
```

### 11.5 删除教师信息时序图

```mermaid
sequenceDiagram
    participant U as 用户
    participant UI as 管理界面
    participant S as 业务服务
    participant DB as 数据库

    U->>UI: 1. 选择教师记录
    U->>UI: 2. 点击"删除"按钮
    UI->>U: 3. 显示确认删除对话框
    U->>UI: 4. 确认删除操作

    UI->>S: 5. 发送删除请求
    S->>DB: 6. 检查教师关联数据
    DB->>S: 7. 返回关联检查结果

    alt 存在关联数据
        S->>UI: 8a. 返回关联数据警告
        UI->>U: 9a. 显示无法删除提示
    else 无关联数据
        S->>DB: 8b. 执行软删除操作
        Note over S,DB: 软删除：设置状态为"已删除"<br/>而不是物理删除记录
        DB->>S: 9b. 返回删除结果
        S->>UI: 10b. 返回操作结果
        UI->>UI: 11b. 刷新教师列表
        UI->>U: 12b. 显示删除成功提示
    end
```

### 11.6 教师信息管理功能设计说明

#### 11.6.1 功能模块概述

教师信息管理模块是科研管理系统的核心模块之一，负责管理教师的基本信息、学术背景、工作履历等数据。该模块提供完整的CRUD（创建、读取、更新、删除）操作，支持多维度查询和数据统计分析。

#### 11.6.2 主要功能特性

| 功能 | 描述 | 实现方式 |
|------|------|----------|
| 教师信息录入 | 支持单个和批量添加教师信息 | 表单输入 + 数据验证 |
| 信息查询检索 | 多条件组合查询和模糊搜索 | SQL查询 + 分页显示 |
| 信息修改更新 | 在线编辑教师信息 | 表单编辑 + 版本控制 |
| 信息删除管理 | 软删除机制保留历史数据 | 状态标记 + 权限控制 |
| 数据导入导出 | Excel格式的批量操作 | 文件处理 + 数据转换 |
| 统计分析报表 | 多维度统计和可视化展示 | 数据聚合 + 图表生成 |

#### 11.6.3 数据验证规则

```mermaid
graph LR
    A[数据输入] --> B{基础验证}
    B -->|通过| C{业务验证}
    B -->|失败| D[显示格式错误]
    C -->|通过| E{唯一性验证}
    C -->|失败| F[显示业务错误]
    E -->|通过| G[保存数据]
    E -->|失败| H[显示重复错误]

    style A fill:#e3f2fd
    style G fill:#e8f5e8
    style D fill:#ffebee
    style F fill:#ffebee
    style H fill:#ffebee
```

**验证规则详细说明：**

1. **基础验证**
   - 必填字段非空验证
   - 数据类型格式验证
   - 字段长度限制验证
   - 特殊字符过滤验证

2. **业务验证**
   - 出生日期合理性验证
   - 入职日期逻辑验证
   - 薪资数据范围验证
   - 职称与学历匹配验证

3. **唯一性验证**
   - 用户名唯一性检查
   - 身份证号唯一性检查
   - 邮箱地址唯一性检查
   - 工号唯一性检查

#### 11.6.4 权限控制设计

```mermaid
graph TD
    A[用户登录] --> B{用户角色}
    B -->|系统管理员| C[全部权限]
    B -->|人事管理员| D[管理权限]
    B -->|部门管理员| E[部门权限]
    B -->|普通教师| F[查看权限]

    C --> G[增删改查<br/>导入导出<br/>统计报表]
    D --> H[增删改查<br/>本部门数据<br/>基础统计]
    E --> I[查看修改<br/>本部门教师<br/>部门统计]
    F --> J[查看个人信息<br/>修改个人资料]

    style A fill:#e3f2fd
    style C fill:#c8e6c9
    style D fill:#dcedc8
    style E fill:#f0f4c3
    style F fill:#fff9c4
```

#### 11.6.5 异常处理机制

| 异常类型 | 处理策略 | 用户提示 |
|----------|----------|----------|
| 网络连接异常 | 自动重试3次 | "网络连接不稳定，请稍后重试" |
| 数据库连接异常 | 连接池重连 | "系统繁忙，请稍后重试" |
| 数据验证异常 | 字段级错误提示 | "请检查输入的XXX格式" |
| 权限不足异常 | 记录日志并拒绝 | "您没有执行此操作的权限" |
| 数据冲突异常 | 提供解决方案 | "数据已被其他用户修改，请刷新后重试" |
| 文件上传异常 | 格式和大小检查 | "文件格式不正确或文件过大" |

#### 11.6.6 性能优化策略

1. **数据库优化**
   - 在常用查询字段上建立索引
   - 使用分页查询减少数据传输量
   - 实施查询缓存机制
   - 定期优化数据库表结构

2. **前端优化**
   - 实现懒加载和虚拟滚动
   - 使用防抖技术优化搜索
   - 缓存常用数据减少请求
   - 异步加载提升用户体验

3. **接口优化**
   - 批量操作接口设计
   - 数据压缩传输
   - 接口响应缓存
   - 并发控制和限流

#### 11.6.7 安全设计考虑

1. **数据安全**
   - 敏感信息加密存储
   - 数据传输HTTPS加密
   - 定期数据备份
   - 访问日志记录

2. **操作安全**
   - 重要操作二次确认
   - 操作权限细粒度控制
   - 异常操作监控告警
   - 数据变更审计跟踪

3. **系统安全**
   - SQL注入防护
   - XSS攻击防护
   - CSRF攻击防护
   - 文件上传安全检查

这个教师信息管理功能模块设计提供了完整的业务流程、技术实现和安全保障，为科研管理系统的核心功能提供了坚实的设计基础。

## 12. 科研成果管理功能模块设计

### 12.1 科研成果管理主流程图

```mermaid
flowchart TD
    A[开始] --> B[登录] --> C{成功?}
    C -->|否| D[错误] --> B
    C -->|是| E[成果管理界面] --> F{操作}

    F -->|查询| G[查询] --> H[结果] --> F

    F -->|录入| I[输入成果] --> J{验证}
    J -->|否| K[错误] --> I
    J -->|是| L[保存] --> M{成功?}
    M -->|否| N[失败] --> I
    M -->|是| O[刷新] --> F

    F -->|排名| P[选择成果] --> Q[设置排名] --> R{验证}
    R -->|否| S[错误] --> Q
    R -->|是| T[保存排名] --> U{成功?}
    U -->|否| V[失败] --> Q
    U -->|是| O

    F -->|统计| W[统计分析] --> X[生成报表] --> F

    F -->|退出| Y[结束]

    style A fill:#e1f5fe
    style Y fill:#ffebee
    style C fill:#fff3e0
    style J fill:#fff3e0
    style R fill:#fff3e0
```

### 12.2 成果录入时序图

```mermaid
sequenceDiagram
    participant U as 用户
    participant UI as 成果界面
    participant V as 验证器
    participant S as 成果服务
    participant DB as 数据库

    U->>UI: 1. 点击"录入成果"
    UI->>UI: 2. 打开录入对话框
    U->>UI: 3. 输入成果信息
    U->>UI: 4. 选择关联项目
    U->>UI: 5. 点击"保存"
    UI->>V: 6. 提交数据验证
    V->>V: 7. 执行格式验证

    alt 验证失败
        V->>UI: 8a. 返回验证错误
        UI->>U: 9a. 显示错误提示
    else 验证成功
        V->>S: 8b. 传递验证数据
        S->>DB: 9b. 检查项目关联
        DB->>S: 10b. 返回检查结果

        alt 项目不存在
            S->>UI: 11a. 返回项目错误
            UI->>U: 12a. 显示项目不存在
        else 项目存在
            S->>DB: 11b. 插入成果记录
            DB->>S: 12b. 返回插入结果
            S->>UI: 13b. 返回操作结果
            UI->>UI: 14b. 关闭对话框
            UI->>UI: 15b. 刷新成果列表
            UI->>U: 16b. 显示成功提示
        end
    end
```

### 12.3 成果排名管理时序图

```mermaid
sequenceDiagram
    participant U as 用户
    participant UI as 排名界面
    participant S as 排名服务
    participant DB as 数据库

    U->>UI: 1. 选择成果记录
    U->>UI: 2. 点击"管理排名"
    UI->>S: 3. 请求成果详情
    S->>DB: 4. 查询成果信息
    DB->>S: 5. 返回成果数据
    S->>DB: 6. 查询现有排名
    DB->>S: 7. 返回排名列表
    S->>UI: 8. 返回完整信息
    UI->>UI: 9. 显示排名管理界面

    U->>UI: 10. 添加/修改教师排名
    U->>UI: 11. 设置排名顺序
    U->>UI: 12. 设置贡献比例
    U->>UI: 13. 点击"保存排名"

    UI->>S: 14. 提交排名数据
    S->>S: 15. 验证排名逻辑

    alt 验证失败
        S->>UI: 16a. 返回验证错误
        UI->>U: 17a. 显示错误提示
    else 验证成功
        S->>DB: 16b. 更新排名表
        DB->>S: 17b. 返回更新结果
        S->>UI: 18b. 返回操作结果
        UI->>UI: 19b. 刷新排名显示
        UI->>U: 20b. 显示成功提示
    end
```

### 12.4 成果查询统计时序图

```mermaid
sequenceDiagram
    participant U as 用户
    participant UI as 查询界面
    participant S as 查询服务
    participant A as 分析引擎
    participant DB as 数据库

    U->>UI: 1. 设置查询条件
    U->>UI: 2. 选择统计维度
    U->>UI: 3. 点击"查询统计"
    UI->>S: 4. 发送查询请求
    S->>S: 5. 构建查询语句
    S->>DB: 6. 执行数据查询
    DB->>S: 7. 返回原始数据
    S->>A: 8. 请求数据分析
    A->>A: 9. 执行统计计算
    A->>A: 10. 生成图表数据
    A->>S: 11. 返回分析结果
    S->>UI: 12. 返回统计数据
    UI->>UI: 13. 渲染图表和表格
    UI->>U: 14. 显示统计结果

    opt 导出报表
        U->>UI: 15. 点击"导出"
        UI->>S: 16. 请求导出
        S->>S: 17. 生成报表文件
        S->>UI: 18. 返回文件链接
        UI->>U: 19. 提供下载
    end
```

### 12.5 成果质量评价时序图

```mermaid
sequenceDiagram
    participant U as 用户
    participant UI as 评价界面
    participant E as 评价引擎
    participant S as 成果服务
    participant DB as 数据库

    U->>UI: 1. 选择成果进行评价
    UI->>S: 2. 请求成果详情
    S->>DB: 3. 查询成果信息
    DB->>S: 4. 返回成果数据
    S->>UI: 5. 返回成果信息
    UI->>UI: 6. 显示评价界面

    U->>UI: 7. 输入评价指标
    U->>UI: 8. 设置权重系数
    U->>UI: 9. 点击"计算评分"

    UI->>E: 10. 提交评价数据
    E->>E: 11. 执行评价算法
    E->>E: 12. 计算综合得分
    E->>UI: 13. 返回评价结果

    UI->>U: 14. 显示评价得分

    opt 保存评价
        U->>UI: 15. 确认保存评价
        UI->>S: 16. 提交评价结果
        S->>DB: 17. 更新成果评分
        DB->>S: 18. 返回更新结果
        S->>UI: 19. 返回保存结果
        UI->>U: 20. 显示保存成功
    end
```

### 12.6 科研成果管理功能设计说明

#### 12.6.1 功能模块概述

科研成果管理模块是科研管理系统的重要组成部分，负责管理各类科研成果的录入、分类、排名、评价和统计分析。该模块支持多种成果类型，包括学术论文、专利、获奖、著作等，提供完整的成果生命周期管理。

#### 12.6.2 主要功能特性

| 功能模块 | 核心功能 | 实现方式 |
|----------|----------|----------|
| 成果录入 | 多类型成果信息录入 | 分类表单 + 动态验证 |
| 成果分类 | 按类别、级别、领域分类 | 树形结构 + 标签系统 |
| 排名管理 | 教师在成果中的排名 | 拖拽排序 + 贡献度计算 |
| 质量评价 | 多维度质量评估 | 评价算法 + 权重配置 |
| 统计分析 | 多维度数据统计 | SQL聚合 + 可视化图表 |
| 报表生成 | 各类统计报表 | 模板引擎 + 数据导出 |

#### 12.6.3 成果分类体系

```mermaid
graph TD
    A[科研成果] --> B[学术论文]
    A --> C[知识产权]
    A --> D[科技奖励]
    A --> E[著作教材]
    A --> F[标准规范]

    B --> B1[SCI论文]
    B --> B2[EI论文]
    B --> B3[核心期刊]
    B --> B4[会议论文]

    C --> C1[发明专利]
    C --> C2[实用新型]
    C --> C3[外观设计]
    C --> C4[软件著作权]

    D --> D1[国家级奖励]
    D --> D2[省部级奖励]
    D --> D3[行业奖励]
    D --> D4[学会奖励]

    E --> E1[学术专著]
    E --> E2[编著]
    E --> E3[教材]
    E --> E4[译著]

    F --> F1[国家标准]
    F --> F2[行业标准]
    F --> F3[企业标准]
    F --> F4[技术规范]

    style A fill:#e3f2fd
    style B fill:#e8f5e8
    style C fill:#fff3e0
    style D fill:#fce4ec
    style E fill:#f3e5f5
    style F fill:#e0f2f1
```

#### 12.6.4 成果质量评价体系

| 评价维度 | 权重 | 评价指标 | 计算方法 |
|----------|------|----------|----------|
| 学术影响力 | 40% | 影响因子、被引次数、下载量 | 加权平均 |
| 创新程度 | 30% | 原创性、技术先进性、理论贡献 | 专家评分 |
| 应用价值 | 20% | 实际应用、经济效益、社会效益 | 量化指标 |
| 国际化水平 | 10% | 国际合作、外文发表、国际影响 | 标准化评分 |

**质量评分算法：**
```
总分 = Σ(维度得分 × 权重) × 类别系数
其中：
- 维度得分：0-100分
- 权重：各维度权重之和为100%
- 类别系数：不同成果类型的调节系数
```

#### 12.6.5 排名管理规则

```mermaid
graph LR
    A[排名设置] --> B{排名类型}
    B -->|第一作者| C[贡献度80%]
    B -->|通讯作者| D[贡献度60%]
    B -->|共同第一| E[贡献度平分]
    B -->|其他作者| F[贡献度递减]

    C --> G[排名验证]
    D --> G
    E --> G
    F --> G

    G --> H{总和检查}
    H -->|=100%| I[保存成功]
    H -->|≠100%| J[调整提示]

    style A fill:#e3f2fd
    style I fill:#e8f5e8
    style J fill:#ffebee
```

**排名管理约束：**
1. **唯一性约束**：同一成果中每个教师只能有一个排名
2. **完整性约束**：所有参与教师的贡献度总和必须为100%
3. **逻辑性约束**：第一作者贡献度不能低于其他作者
4. **一致性约束**：排名顺序与贡献度大小保持一致

#### 12.6.6 数据验证机制

```mermaid
flowchart TD
    A[数据输入] --> B{基础验证}
    B -->|通过| C{业务验证}
    B -->|失败| D[格式错误]

    C -->|通过| E{关联验证}
    C -->|失败| F[业务错误]

    E -->|通过| G{完整性验证}
    E -->|失败| H[关联错误]

    G -->|通过| I[保存成功]
    G -->|失败| J[完整性错误]

    style A fill:#e3f2fd
    style I fill:#e8f5e8
    style D fill:#ffebee
    style F fill:#ffebee
    style H fill:#ffebee
    style J fill:#ffebee
```

**验证层次说明：**

1. **基础验证**
   - 必填字段非空检查
   - 数据类型格式验证
   - 字段长度限制验证
   - 特殊字符过滤验证

2. **业务验证**
   - 成果类别与字段匹配验证
   - 日期逻辑合理性验证
   - 数值范围有效性验证
   - 枚举值合法性验证

3. **关联验证**
   - 项目关联存在性验证
   - 教师关联有效性验证
   - 部门权限匹配验证
   - 时间范围一致性验证

4. **完整性验证**
   - 排名数据完整性验证
   - 贡献度总和验证
   - 必要附件完整性验证
   - 审核流程完整性验证

#### 12.6.7 统计分析功能

| 统计维度 | 统计指标 | 图表类型 | 应用场景 |
|----------|----------|----------|----------|
| 时间维度 | 年度成果趋势 | 折线图 | 发展趋势分析 |
| 类别维度 | 成果类型分布 | 饼图 | 结构分析 |
| 人员维度 | 教师成果排名 | 柱状图 | 绩效评估 |
| 部门维度 | 部门成果对比 | 雷达图 | 部门评比 |
| 质量维度 | 成果质量分布 | 散点图 | 质量分析 |
| 影响维度 | 影响因子分析 | 热力图 | 影响力评估 |

#### 12.6.8 安全与权限控制

```mermaid
graph TD
    A[用户登录] --> B{角色判断}
    B -->|系统管理员| C[全部权限]
    B -->|科研管理员| D[管理权限]
    B -->|项目负责人| E[项目权限]
    B -->|普通教师| F[个人权限]

    C --> G[增删改查<br/>统计分析<br/>系统配置]
    D --> H[审核管理<br/>统计报表<br/>质量评价]
    E --> I[项目成果<br/>团队排名<br/>数据维护]
    F --> J[个人成果<br/>查看统计<br/>排名确认]

    style A fill:#e3f2fd
    style C fill:#c8e6c9
    style D fill:#dcedc8
    style E fill:#f0f4c3
    style F fill:#fff9c4
```

这个科研成果管理功能模块设计提供了完整的成果管理解决方案，通过详细的时序图和流程设计，为科研管理系统的成果管理功能提供了全面的技术指导和实现方案。
```
```
