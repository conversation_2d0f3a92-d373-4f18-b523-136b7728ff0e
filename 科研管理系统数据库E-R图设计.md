# 科研管理系统数据库E-R图设计

## 1. 概念模型设计

### 1.1 传统E-R图概览

```
                    ┌─────────────┐
                    │    部门     │
                    │ DEPARTMENT  │
                    └──────┬──────┘
                           │
                           │ 1:N (包含)
                           │
                    ┌──────▼──────┐
                    │    教师     │
                    │  TEACHER    │
                    └──────┬──────┘
                           │
                    ┌──────┴──────┐
                    │             │
                    │ 1:N (负责)  │ M:N (参与)
                    │             │
            ┌───────▼──────┐     ┌▼─────────────┐
            │     项目     │     │   项目参与   │
            │   PROJECT    │     │PARTICIPATION │
            └───────┬──────┘     └──────────────┘
                    │
                    │ 1:N (产生)
                    │
            ┌───────▼──────┐
            │     成果     │     ┌──────────────┐
            │ ACHIEVEMENT  │────▶│   成果排名   │
            └──────────────┘ M:N │   RANKING    │
                                 └──────────────┘
```

### 1.2 详细E-R图（Chen表示法）

```
    ┌─────────┐                    ┌─────────┐
    │  部门   │                    │  教师   │
    │DEPARTMENT│                   │ TEACHER │
    └────┬────┘                    └────┬────┘
         │                              │
    dept_id(PK)                   teacher_id(PK)
    dept_name                     username
    dept_code                     password
    description                   name
                                  gender
         │                        birth_date
         │                        ethnicity
         │ 包含                   education
         │ 1:N                    title
         │                        base_salary
         ▼                        post_salary
    ┌─────────┐                   bonus_salary
    │         │                   dept_id(FK)
    │         │                        │
    │         │                        │
    │         │                        │ 负责
    │         │                        │ 1:N
    │         │                        ▼
    │         │                   ┌─────────┐
    │         │                   │  项目   │
    │         │                   │ PROJECT │
    │         │                   └────┬────┘
    │         │                        │
    │         │                   project_id(PK)
    │         │                   project_name
    │         │                   project_type
    │         │                   manager_id(FK)
    │         │                   fund
    │         │                   start_date
    │         │                   end_date
    │         │                   status
    │         │                        │
    │         │                        │ 产生
    │         │                        │ 1:N
    │         │                        ▼
    │         │                   ┌─────────┐
    │         │                   │  成果   │
    │         │                   │ACHIEVEMENT│
    │         │                   └────┬────┘
    │         │                        │
    │         │                   achievement_id(PK)
    │         │                   project_id(FK)
    │         │                   title
    │         │                   category
    │         │                   level
    │         │                   grade
    │         │                   approval_date
    │         │                        │
    │         │                        │
    │         │    ┌─────────────────────┘
    │         │    │
    │         │    │ 参与(M:N)
    │         │    │
    │         │    ▼
    │         │ ┌─────────┐         ┌─────────┐
    │         │ │项目参与 │         │成果排名 │
    │         │ │PARTICIPATION│     │ RANKING │
    │         │ └─────────┘         └─────────┘
    │         │      │                   │
    │         │ teacher_id(FK)      achievement_id(FK)
    │         │ project_id(FK)      teacher_id(FK)
    │         │ role                ranking
    │         │ contribution_rate   author_type
    │         │ join_date          contribution_rate
    │         │                    confirm_date
    │         │                         │
    │         └─────────────────────────┘
                     贡献(M:N)
```

### 1.3 E-R图符号说明

在传统的E-R图中，我们使用以下符号：

- **矩形** □：表示实体（Entity）
- **菱形** ◇：表示关系（Relationship）
- **椭圆** ○：表示属性（Attribute）
- **下划线**：表示主键属性
- **虚线椭圆**：表示派生属性
- **双线椭圆**：表示多值属性
- **连接线**：表示实体与关系、关系与属性之间的联系
- **基数标记**：1、N、M 表示关系的基数

### 1.4 基数约束说明

- **1:1**（一对一）：一个实体实例只能与另一个实体的一个实例相关联
- **1:N**（一对多）：一个实体实例可以与另一个实体的多个实例相关联
- **M:N**（多对多）：一个实体的多个实例可以与另一个实体的多个实例相关联

## 2. 实体详细设计

### 2.1 实体集合（Entity Sets）

#### 2.1.1 部门实体 (DEPARTMENT)

**实体描述：** 表示学校的各个部门组织结构

**主要属性：**
- **dept_id**（主键）：部门唯一标识符
- **dept_name**：部门名称
- **dept_code**：部门编码
- **description**：部门描述

**属性详细说明：**

| 属性名 | 属性类型 | 约束 | 描述 |
|--------|----------|------|------|
| dept_id | 简单属性 | 主键 | 部门唯一标识符 |
| dept_name | 简单属性 | 非空，唯一 | 部门名称 |
| dept_code | 简单属性 | 唯一 | 部门编码 |
| description | 简单属性 | 可空 | 部门描述信息 |

#### 2.1.2 教师实体 (TEACHER)

**实体描述：** 表示学校的教师，包含个人信息和工作信息

**主要属性：**
- **teacher_id**（主键）：教师唯一标识符
- **username**：登录用户名
- **name**：教师姓名
- **gender**：性别
- **birth_date**：出生日期
- **ethnicity**：民族
- **education**：学历
- **title**：职称
- **base_salary**：基本工资
- **post_salary**：岗位工资
- **bonus_salary**：奖金
- **dept_id**（外键）：所属部门

**属性详细说明：**

| 属性名 | 属性类型 | 约束 | 描述 |
|--------|----------|------|------|
| teacher_id | 简单属性 | 主键 | 教师唯一标识符 |
| username | 简单属性 | 非空，唯一 | 登录用户名 |
| name | 简单属性 | 非空 | 教师姓名 |
| gender | 简单属性 | 可空 | 性别（男/女/其他） |
| birth_date | 简单属性 | 可空 | 出生日期 |
| ethnicity | 简单属性 | 可空 | 民族 |
| education | 简单属性 | 可空 | 最高学历 |
| title | 简单属性 | 可空 | 职称 |
| base_salary | 简单属性 | 可空 | 基本工资 |
| post_salary | 简单属性 | 可空 | 岗位工资 |
| bonus_salary | 简单属性 | 可空 | 绩效奖金 |
| dept_id | 简单属性 | 外键 | 所属部门ID |

#### 2.1.3 项目实体 (PROJECT)

**实体描述：** 表示科研项目，包含项目的基本信息和管理数据

**主要属性：**
- **project_id**（主键）：项目唯一标识符
- **project_name**：项目名称
- **project_type**：项目类型
- **manager_id**（外键）：项目负责人ID
- **fund**：项目经费
- **start_date**：开始日期
- **end_date**：结束日期
- **status**：项目状态

**属性详细说明：**

| 属性名 | 属性类型 | 约束 | 描述 |
|--------|----------|------|------|
| project_id | 简单属性 | 主键 | 项目唯一标识符 |
| project_name | 简单属性 | 非空 | 项目名称 |
| project_type | 简单属性 | 可空 | 项目类型 |
| manager_id | 简单属性 | 外键 | 项目负责人ID |
| fund | 简单属性 | 可空 | 项目经费 |
| start_date | 简单属性 | 可空 | 项目开始日期 |
| end_date | 简单属性 | 可空 | 项目结束日期 |
| status | 简单属性 | 可空 | 项目状态 |

#### 2.1.4 成果实体 (ACHIEVEMENT)

**实体描述：** 表示科研成果，包含成果的基本信息和评价数据

**主要属性：**
- **achievement_id**（主键）：成果唯一标识符
- **project_id**（外键）：关联项目ID
- **title**：成果标题
- **category**：成果类别
- **level**：成果级别
- **grade**：成果等级
- **approval_date**：批准日期

**属性详细说明：**

| 属性名 | 属性类型 | 约束 | 描述 |
|--------|----------|------|------|
| achievement_id | 简单属性 | 主键 | 成果唯一标识符 |
| project_id | 简单属性 | 外键 | 关联项目ID |
| title | 简单属性 | 非空 | 成果标题 |
| category | 简单属性 | 非空 | 成果类别 |
| level | 简单属性 | 可空 | 成果级别 |
| grade | 简单属性 | 可空 | 成果等级 |
| approval_date | 简单属性 | 可空 | 批准日期 |

### 2.2 关系集合（Relationship Sets）

#### 2.2.1 包含关系 (BELONG)

**关系描述：** 部门与教师之间的一对多关系

- **参与实体：** 部门（DEPARTMENT）、教师（TEACHER）
- **关系类型：** 1:N（一个部门包含多名教师，一名教师属于一个部门）
- **关系属性：** 无

#### 2.2.2 负责关系 (MANAGE)

**关系描述：** 教师与项目之间的一对多管理关系

- **参与实体：** 教师（TEACHER）、项目（PROJECT）
- **关系类型：** 1:N（一名教师可以负责多个项目，一个项目只有一名负责人）
- **关系属性：** 无

#### 2.2.3 参与关系 (PARTICIPATE)

**关系描述：** 教师与项目之间的多对多参与关系

- **参与实体：** 教师（TEACHER）、项目（PROJECT）
- **关系类型：** M:N（一名教师可以参与多个项目，一个项目可以有多名教师参与）
- **关系属性：**
  - **role**：参与角色
  - **contribution_rate**：贡献比例
  - **join_date**：加入日期

#### 2.2.4 产生关系 (PRODUCE)

**关系描述：** 项目与成果之间的一对多产生关系

- **参与实体：** 项目（PROJECT）、成果（ACHIEVEMENT）
- **关系类型：** 1:N（一个项目可以产生多个成果，一个成果属于一个项目）
- **关系属性：** 无

#### 2.2.5 贡献关系 (CONTRIBUTE)

**关系描述：** 教师与成果之间的多对多贡献关系

- **参与实体：** 教师（TEACHER）、成果（ACHIEVEMENT）
- **关系类型：** M:N（一名教师可以贡献多个成果，一个成果可以有多名教师贡献）
- **关系属性：**
  - **ranking**：排名
  - **author_type**：作者类型
  - **contribution_rate**：贡献比例

## 3. 关系实体设计

### 3.1 项目参与关系 (PROJECT_PARTICIPATION)

**关系描述：** 教师与项目之间的多对多参与关系

| 属性名 | 数据类型 | 约束条件 | 描述 |
|--------|----------|----------|------|
| teacher_id | BIGINT | FOREIGN KEY | 教师ID |
| project_id | INT | FOREIGN KEY | 项目ID |
| role | VARCHAR(100) | | 参与角色 |
| responsibility | TEXT | | 职责描述 |
| contribution_rate | DECIMAL(5,2) | CHECK (contribution_rate BETWEEN 0 AND 100) | 贡献比例(%) |
| workload | DECIMAL(8,2) | | 工作量(人月) |
| join_date | DATE | | 加入日期 |
| leave_date | DATE | | 离开日期 |
| status | ENUM('参与中','已完成','已退出') | DEFAULT '参与中' | 参与状态 |
| performance_rating | VARCHAR(20) | | 绩效评级 |
| remarks | TEXT | | 备注 |
| create_time | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| update_time | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

**主键：** (teacher_id, project_id)

### 3.2 成果排名关系 (ACHIEVEMENT_RANKING)

**关系描述：** 教师在成果中的排名和贡献关系

| 属性名 | 数据类型 | 约束条件 | 描述 |
|--------|----------|----------|------|
| achievement_id | INT | FOREIGN KEY | 成果ID |
| teacher_id | BIGINT | FOREIGN KEY | 教师ID |
| ranking | INT | NOT NULL | 排名顺序 |
| author_type | ENUM('第一作者','通讯作者','共同第一作者','共同通讯作者','其他作者') | | 作者类型 |
| contribution_rate | DECIMAL(5,2) | CHECK (contribution_rate BETWEEN 0 AND 100) | 贡献比例(%) |
| contribution_description | TEXT | | 贡献描述 |
| signature_unit | VARCHAR(200) | | 署名单位 |
| is_corresponding | BOOLEAN | DEFAULT FALSE | 是否通讯作者 |
| confirm_date | DATE | | 确认日期 |
| confirm_status | ENUM('待确认','已确认','有争议') | DEFAULT '待确认' | 确认状态 |
| remarks | TEXT | | 备注 |
| create_time | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| update_time | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

**主键：** (achievement_id, teacher_id)

## 3. E-R图完整性约束

### 3.1 实体完整性约束

每个实体集合都必须有一个主键来唯一标识实体实例：

- **部门实体**：dept_id 作为主键
- **教师实体**：teacher_id 作为主键
- **项目实体**：project_id 作为主键
- **成果实体**：achievement_id 作为主键

### 3.2 参照完整性约束

外键约束确保关系的一致性：

- **教师.dept_id** 必须引用存在的 **部门.dept_id**
- **项目.manager_id** 必须引用存在的 **教师.teacher_id**
- **成果.project_id** 必须引用存在的 **项目.project_id**

### 3.3 基数约束

关系的基数约束限制了实体间的关联数量：

1. **包含关系（部门-教师）**：1:N
   - 每个部门可以包含0到多名教师
   - 每名教师必须属于且仅属于1个部门

2. **负责关系（教师-项目）**：1:N
   - 每名教师可以负责0到多个项目
   - 每个项目必须有且仅有1名负责人

3. **参与关系（教师-项目）**：M:N
   - 每名教师可以参与0到多个项目
   - 每个项目可以有0到多名教师参与

4. **产生关系（项目-成果）**：1:N
   - 每个项目可以产生0到多个成果
   - 每个成果必须属于且仅属于1个项目

5. **贡献关系（教师-成果）**：M:N
   - 每名教师可以贡献0到多个成果
   - 每个成果可以有1到多名教师贡献

### 3.4 域约束

属性值必须符合定义的域：

- **性别**：只能是'男'、'女'、'其他'
- **项目状态**：只能是'申报中'、'立项'、'执行中'、'结题'、'终止'
- **成果等级**：必须是正整数
- **贡献比例**：必须在0-100之间
- **日期**：必须是有效的日期格式

## 4. E-R图转换为关系模式

### 4.1 实体转换规则

每个实体集合转换为一个关系模式：

- **部门** → DEPARTMENT(dept_id, dept_name, dept_code, description)
- **教师** → TEACHER(teacher_id, username, name, gender, birth_date, ethnicity, education, title, base_salary, post_salary, bonus_salary, dept_id)
- **项目** → PROJECT(project_id, project_name, project_type, manager_id, fund, start_date, end_date, status)
- **成果** → ACHIEVEMENT(achievement_id, project_id, title, category, level, grade, approval_date)

### 4.2 关系转换规则

#### 4.2.1 一对多关系转换

一对多关系通过在"多"端添加外键实现：

- **包含关系**：在TEACHER表中添加dept_id外键
- **负责关系**：在PROJECT表中添加manager_id外键
- **产生关系**：在ACHIEVEMENT表中添加project_id外键

#### 4.2.2 多对多关系转换

多对多关系需要创建独立的关系表：

- **参与关系** → PROJECT_PARTICIPATION(teacher_id, project_id, role, contribution_rate, join_date)
- **贡献关系** → ACHIEVEMENT_RANKING(achievement_id, teacher_id, ranking, author_type, contribution_rate)

## 5. 数据完整性约束

### 5.1 实体完整性
- 每个实体都有唯一的主键标识
- 主键不能为空且唯一

### 5.2 参照完整性
- 所有外键都必须引用存在的主键值
- 级联更新和删除策略

### 5.3 用户定义完整性
- 日期逻辑约束：结束日期不能早于开始日期
- 数值范围约束：贡献比例在0-100之间
- 枚举值约束：状态字段只能取预定义值
- 唯一性约束：用户名、身份证号等唯一

### 5.4 域完整性
- 数据类型约束
- 长度限制
- 格式验证（如邮箱格式、电话格式）

## 6. 索引设计建议

### 6.1 主键索引
- 所有主键自动创建聚集索引

### 6.2 外键索引
```sql
-- 教师表索引
CREATE INDEX idx_teacher_dept ON teacher(dept_id);
CREATE INDEX idx_teacher_username ON teacher(username);
CREATE INDEX idx_teacher_name ON teacher(name);
CREATE INDEX idx_teacher_title ON teacher(title);

-- 项目表索引
CREATE INDEX idx_project_manager ON project(manager_id);
CREATE INDEX idx_project_name ON project(project_name);
CREATE INDEX idx_project_type ON project(project_type);
CREATE INDEX idx_project_status ON project(status);

-- 成果表索引
CREATE INDEX idx_achievement_project ON achievement(project_id);
CREATE INDEX idx_achievement_category ON achievement(category);
CREATE INDEX idx_achievement_level ON achievement(level);
CREATE INDEX idx_achievement_date ON achievement(publication_date);

-- 关系表索引
CREATE INDEX idx_participation_teacher ON project_participation(teacher_id);
CREATE INDEX idx_participation_project ON project_participation(project_id);
CREATE INDEX idx_ranking_teacher ON achievement_ranking(teacher_id);
CREATE INDEX idx_ranking_achievement ON achievement_ranking(achievement_id);
```

### 6.3 复合索引
```sql
-- 常用查询组合索引
CREATE INDEX idx_teacher_dept_title ON teacher(dept_id, title);
CREATE INDEX idx_project_manager_status ON project(manager_id, status);
CREATE INDEX idx_achievement_project_category ON achievement(project_id, category);
```

## 7. 视图设计建议

### 7.1 教师项目成果统计视图
```sql
CREATE VIEW v_teacher_statistics AS
SELECT 
    t.teacher_id,
    t.name,
    t.dept_id,
    COUNT(DISTINCT pp.project_id) as project_count,
    COUNT(DISTINCT ar.achievement_id) as achievement_count,
    SUM(p.total_fund * pp.contribution_rate / 100) as total_funding
FROM teacher t
LEFT JOIN project_participation pp ON t.teacher_id = pp.teacher_id
LEFT JOIN project p ON pp.project_id = p.project_id
LEFT JOIN achievement_ranking ar ON t.teacher_id = ar.teacher_id
GROUP BY t.teacher_id, t.name, t.dept_id;
```

### 7.2 部门科研统计视图
```sql
CREATE VIEW v_department_statistics AS
SELECT 
    d.dept_id,
    d.dept_name,
    COUNT(DISTINCT t.teacher_id) as teacher_count,
    COUNT(DISTINCT p.project_id) as project_count,
    COUNT(DISTINCT a.achievement_id) as achievement_count,
    SUM(p.total_fund) as total_funding
FROM department d
LEFT JOIN teacher t ON d.dept_id = t.dept_id
LEFT JOIN project p ON t.teacher_id = p.manager_id
LEFT JOIN achievement a ON p.project_id = a.project_id
GROUP BY d.dept_id, d.dept_name;
```

## 8. 数据库建表SQL语句

### 8.1 创建数据库
```sql
-- 创建数据库
CREATE DATABASE research_management_system
CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci;

USE research_management_system;
```

### 8.2 创建表结构

#### 8.2.1 部门表
```sql
CREATE TABLE department (
    dept_id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '部门ID',
    dept_name VARCHAR(100) NOT NULL UNIQUE COMMENT '部门名称',
    dept_code VARCHAR(20) UNIQUE COMMENT '部门编码',
    description TEXT COMMENT '部门描述',
    create_date DATE COMMENT '成立日期',
    parent_dept_id BIGINT COMMENT '上级部门ID',
    dept_level INT DEFAULT 1 COMMENT '部门层级',
    contact_phone VARCHAR(20) COMMENT '联系电话',
    contact_email VARCHAR(100) COMMENT '联系邮箱',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (parent_dept_id) REFERENCES department(dept_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='部门信息表';
```

#### 8.2.2 教师表
```sql
CREATE TABLE teacher (
    teacher_id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '教师ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码',
    name VARCHAR(100) NOT NULL COMMENT '姓名',
    gender ENUM('男','女','其他') COMMENT '性别',
    birth_date DATE COMMENT '出生日期',
    id_card VARCHAR(18) UNIQUE COMMENT '身份证号',
    ethnicity VARCHAR(50) COMMENT '民族',
    education VARCHAR(100) COMMENT '最高学历',
    degree VARCHAR(50) COMMENT '最高学位',
    graduate_school VARCHAR(200) COMMENT '毕业院校',
    major VARCHAR(100) COMMENT '专业方向',
    start_work_date DATE COMMENT '参加工作日期',
    join_date DATE COMMENT '入职日期',
    title VARCHAR(100) COMMENT '职称',
    title_date DATE COMMENT '职称获得日期',
    research_direction TEXT COMMENT '研究方向',
    base_salary DECIMAL(10,2) COMMENT '基本工资',
    post_salary DECIMAL(10,2) COMMENT '岗位工资',
    bonus_salary DECIMAL(10,2) COMMENT '绩效奖金',
    dept_id BIGINT COMMENT '所属部门ID',
    phone VARCHAR(20) COMMENT '联系电话',
    email VARCHAR(100) COMMENT '邮箱地址',
    office_location VARCHAR(200) COMMENT '办公地点',
    status ENUM('在职','离职','退休') DEFAULT '在职' COMMENT '工作状态',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (dept_id) REFERENCES department(dept_id),
    INDEX idx_teacher_dept (dept_id),
    INDEX idx_teacher_username (username),
    INDEX idx_teacher_name (name),
    INDEX idx_teacher_title (title)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='教师信息表';
```

#### 8.2.3 项目表
```sql
CREATE TABLE project (
    project_id INT AUTO_INCREMENT PRIMARY KEY COMMENT '项目ID',
    project_code VARCHAR(50) UNIQUE COMMENT '项目编号',
    project_name VARCHAR(500) NOT NULL COMMENT '项目名称',
    project_type VARCHAR(100) COMMENT '项目类型',
    project_level VARCHAR(50) COMMENT '项目级别',
    funding_agency VARCHAR(200) COMMENT '资助机构',
    manager_id BIGINT COMMENT '项目负责人ID',
    total_fund DECIMAL(15,2) COMMENT '项目总经费',
    approved_fund DECIMAL(15,2) COMMENT '批准经费',
    start_date DATE COMMENT '项目开始日期',
    end_date DATE COMMENT '项目结束日期',
    actual_start_date DATE COMMENT '实际开始日期',
    actual_end_date DATE COMMENT '实际结束日期',
    status ENUM('申报中','立项','执行中','结题','终止') DEFAULT '申报中' COMMENT '项目状态',
    research_field VARCHAR(200) COMMENT '研究领域',
    keywords TEXT COMMENT '关键词',
    abstract TEXT COMMENT '项目摘要',
    objectives TEXT COMMENT '项目目标',
    methodology TEXT COMMENT '研究方法',
    expected_results TEXT COMMENT '预期成果',
    risk_assessment TEXT COMMENT '风险评估',
    progress_report TEXT COMMENT '进展报告',
    final_report TEXT COMMENT '结题报告',
    evaluation_result VARCHAR(50) COMMENT '评估结果',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (manager_id) REFERENCES teacher(teacher_id),
    INDEX idx_project_manager (manager_id),
    INDEX idx_project_name (project_name),
    INDEX idx_project_type (project_type),
    INDEX idx_project_status (status),
    CONSTRAINT chk_project_dates CHECK (end_date >= start_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目信息表';
```

#### 8.2.4 成果表
```sql
CREATE TABLE achievement (
    achievement_id INT AUTO_INCREMENT PRIMARY KEY COMMENT '成果ID',
    project_id INT COMMENT '关联项目ID',
    title VARCHAR(500) NOT NULL COMMENT '成果标题',
    category VARCHAR(100) NOT NULL COMMENT '成果类别',
    sub_category VARCHAR(100) COMMENT '成果子类别',
    level VARCHAR(50) COMMENT '成果级别',
    grade INT COMMENT '成果等级',
    journal_name VARCHAR(300) COMMENT '期刊名称',
    publisher VARCHAR(200) COMMENT '出版社',
    publication_date DATE COMMENT '发表/出版日期',
    approval_date DATE COMMENT '批准日期',
    volume_issue VARCHAR(50) COMMENT '卷期号',
    page_numbers VARCHAR(50) COMMENT '页码',
    doi VARCHAR(100) COMMENT 'DOI号',
    isbn VARCHAR(20) COMMENT 'ISBN号',
    patent_number VARCHAR(50) COMMENT '专利号',
    award_name VARCHAR(200) COMMENT '奖项名称',
    award_level VARCHAR(50) COMMENT '奖项级别',
    award_organization VARCHAR(200) COMMENT '颁奖机构',
    impact_factor DECIMAL(8,3) COMMENT '影响因子',
    citation_count INT DEFAULT 0 COMMENT '被引次数',
    download_count INT DEFAULT 0 COMMENT '下载次数',
    h_index DECIMAL(8,3) COMMENT 'H指数',
    quality_score DECIMAL(5,2) COMMENT '质量评分',
    abstract TEXT COMMENT '成果摘要',
    keywords TEXT COMMENT '关键词',
    research_field VARCHAR(200) COMMENT '研究领域',
    language VARCHAR(50) DEFAULT '中文' COMMENT '语言',
    country VARCHAR(100) COMMENT '国家/地区',
    funding_info TEXT COMMENT '资助信息',
    collaboration_type VARCHAR(50) COMMENT '合作类型',
    open_access BOOLEAN DEFAULT FALSE COMMENT '是否开放获取',
    status ENUM('草稿','提交','发表','获奖','专利授权') DEFAULT '草稿' COMMENT '状态',
    file_path VARCHAR(500) COMMENT '文件路径',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (project_id) REFERENCES project(project_id),
    INDEX idx_achievement_project (project_id),
    INDEX idx_achievement_category (category),
    INDEX idx_achievement_level (level),
    INDEX idx_achievement_date (publication_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='成果信息表';
```

#### 8.2.5 项目参与表
```sql
CREATE TABLE project_participation (
    teacher_id BIGINT COMMENT '教师ID',
    project_id INT COMMENT '项目ID',
    role VARCHAR(100) COMMENT '参与角色',
    responsibility TEXT COMMENT '职责描述',
    contribution_rate DECIMAL(5,2) COMMENT '贡献比例(%)',
    workload DECIMAL(8,2) COMMENT '工作量(人月)',
    join_date DATE COMMENT '加入日期',
    leave_date DATE COMMENT '离开日期',
    status ENUM('参与中','已完成','已退出') DEFAULT '参与中' COMMENT '参与状态',
    performance_rating VARCHAR(20) COMMENT '绩效评级',
    remarks TEXT COMMENT '备注',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (teacher_id, project_id),
    FOREIGN KEY (teacher_id) REFERENCES teacher(teacher_id),
    FOREIGN KEY (project_id) REFERENCES project(project_id),
    INDEX idx_participation_teacher (teacher_id),
    INDEX idx_participation_project (project_id),
    CONSTRAINT chk_contribution_rate CHECK (contribution_rate BETWEEN 0 AND 100)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目参与表';
```

#### 8.2.6 成果排名表
```sql
CREATE TABLE achievement_ranking (
    achievement_id INT COMMENT '成果ID',
    teacher_id BIGINT COMMENT '教师ID',
    ranking INT NOT NULL COMMENT '排名顺序',
    author_type ENUM('第一作者','通讯作者','共同第一作者','共同通讯作者','其他作者') COMMENT '作者类型',
    contribution_rate DECIMAL(5,2) COMMENT '贡献比例(%)',
    contribution_description TEXT COMMENT '贡献描述',
    signature_unit VARCHAR(200) COMMENT '署名单位',
    is_corresponding BOOLEAN DEFAULT FALSE COMMENT '是否通讯作者',
    confirm_date DATE COMMENT '确认日期',
    confirm_status ENUM('待确认','已确认','有争议') DEFAULT '待确认' COMMENT '确认状态',
    remarks TEXT COMMENT '备注',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (achievement_id, teacher_id),
    FOREIGN KEY (achievement_id) REFERENCES achievement(achievement_id),
    FOREIGN KEY (teacher_id) REFERENCES teacher(teacher_id),
    INDEX idx_ranking_teacher (teacher_id),
    INDEX idx_ranking_achievement (achievement_id),
    CONSTRAINT chk_ranking_contribution CHECK (contribution_rate BETWEEN 0 AND 100)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='成果排名表';
```

### 8.3 创建视图

#### 8.3.1 教师统计视图
```sql
CREATE VIEW v_teacher_statistics AS
SELECT
    t.teacher_id,
    t.name,
    t.title,
    d.dept_name,
    COUNT(DISTINCT pp.project_id) as project_count,
    COUNT(DISTINCT ar.achievement_id) as achievement_count,
    COALESCE(SUM(p.total_fund * pp.contribution_rate / 100), 0) as total_funding,
    COALESCE(AVG(a.quality_score), 0) as avg_quality_score
FROM teacher t
LEFT JOIN department d ON t.dept_id = d.dept_id
LEFT JOIN project_participation pp ON t.teacher_id = pp.teacher_id
LEFT JOIN project p ON pp.project_id = p.project_id
LEFT JOIN achievement_ranking ar ON t.teacher_id = ar.teacher_id
LEFT JOIN achievement a ON ar.achievement_id = a.achievement_id
GROUP BY t.teacher_id, t.name, t.title, d.dept_name;
```

#### 8.3.2 部门统计视图
```sql
CREATE VIEW v_department_statistics AS
SELECT
    d.dept_id,
    d.dept_name,
    COUNT(DISTINCT t.teacher_id) as teacher_count,
    COUNT(DISTINCT p.project_id) as project_count,
    COUNT(DISTINCT a.achievement_id) as achievement_count,
    COALESCE(SUM(p.total_fund), 0) as total_funding,
    COALESCE(AVG(a.quality_score), 0) as avg_quality_score
FROM department d
LEFT JOIN teacher t ON d.dept_id = t.dept_id
LEFT JOIN project p ON t.teacher_id = p.manager_id
LEFT JOIN achievement a ON p.project_id = a.project_id
GROUP BY d.dept_id, d.dept_name;
```

### 8.4 插入示例数据

#### 8.4.1 部门数据
```sql
INSERT INTO department (dept_name, dept_code, description) VALUES
('计算机科学与技术学院', 'CS', '计算机科学与技术相关专业'),
('电子信息工程学院', 'EE', '电子信息工程相关专业'),
('机械工程学院', 'ME', '机械工程相关专业'),
('材料科学与工程学院', 'MSE', '材料科学与工程相关专业');
```

#### 8.4.2 教师数据
```sql
INSERT INTO teacher (username, password, name, gender, title, dept_id, base_salary, post_salary, bonus_salary) VALUES
('admin', SHA2('123456', 256), '张教授', '男', '教授', 1, 8000.00, 3000.00, 2000.00),
('teacher001', SHA2('123456', 256), '李副教授', '女', '副教授', 1, 7000.00, 2500.00, 1500.00),
('teacher002', SHA2('123456', 256), '王讲师', '男', '讲师', 2, 6000.00, 2000.00, 1000.00),
('teacher003', SHA2('123456', 256), '刘助教', '女', '助教', 3, 5000.00, 1500.00, 800.00);
```

这个E-R图设计涵盖了科研管理系统的核心实体和关系，支持复杂的科研管理业务需求，具有良好的扩展性和维护性。通过详细的表结构设计、约束条件和索引优化，确保了数据的完整性、一致性和查询性能。
