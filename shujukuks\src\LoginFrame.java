import javax.swing.*;
import java.awt.*;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

public class LoginFrame extends JFrame {
    private JTextField usernameField;
    private JPasswordField passwordField;

    public LoginFrame() {
        setTitle("科研管理系统 - 登录");
        setSize(350, 180);
        setLocationRelativeTo(null);
        setDefaultCloseOperation(EXIT_ON_CLOSE);
        setLayout(new GridLayout(4, 1, 5, 5));

        JPanel userPanel = new JPanel();
        userPanel.add(new JLabel("用户名:"));
        usernameField = new JTextField(15);
        userPanel.add(usernameField);
        add(userPanel);

        JPanel passPanel = new JPanel();
        passPanel.add(new JLabel("密码:"));
        passwordField = new JPasswordField(15);
        passPanel.add(passwordField);
        add(passPanel);

        JButton loginBtn = new JButton("登录");
        add(loginBtn);

        JLabel tipLabel = new JLabel("", JLabel.CENTER);
        add(tipLabel);

        loginBtn.addActionListener(e -> {
            String user = usernameField.getText().trim();
            String pass = new String(passwordField.getPassword());
            Teacher teacher = checkLogin(user, pass);
            if (teacher != null) {
                tipLabel.setText("✅ 登录成功！");
                this.setVisible(false);
                new MainDashboard(teacher);
            } else {
                tipLabel.setText("❌ 登录失败，账号或密码错误");
            }
        });
        setVisible(true);
    }

    private Teacher checkLogin(String user, String pass) {
        // 这里假设有users表存用户名密码，简单示范，实际应加密密码
        String sql = "SELECT * FROM teacher WHERE username=? AND password=?";
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setString(1, user);
            stmt.setString(2, pass);
            ResultSet rs = stmt.executeQuery();
            if(rs.next()){
                Teacher teacher = new Teacher();
                teacher.setUsername(rs.getString("username"));
                teacher.setPassword(rs.getString("password"));
                teacher.setTeacherId(rs.getLong("teacher_id"));
                teacher.setName(rs.getString("name"));
                teacher.setGender(rs.getString("gender"));
                teacher.setBirthDate(rs.getDate("birth_date"));
                teacher.setEthnicity(rs.getString("ethnicity"));
                teacher.setEducation(rs.getString("education"));
                teacher.setStartWorkDate(rs.getDate("start_work_date"));
                teacher.setTitle(rs.getString("title"));
                teacher.setBaseSalary(rs.getDouble("base_salary"));
                teacher.setPostSalary(rs.getDouble("post_salary"));
                teacher.setBonusSalary(rs.getDouble("bonus_salary"));
                teacher.setDeptId(rs.getLong("dept_id"));
                System.out.println(teacher.getBaseSalary());
                return teacher;
            }
            else{
                return null;
            }

        } catch (SQLException e) {
            JOptionPane.showMessageDialog(this, "数据库错误：" + e.getMessage());
            return null;
        }
    }

}
