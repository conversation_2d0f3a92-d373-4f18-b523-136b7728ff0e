import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.sql.*;

public class TeacherProjectResultViewer extends JFrame {
    private JTextField teacherIdField;
    private JButton queryBtn, backBtn;
    private JTable table;
    private DefaultTableModel model;
    private Teacher currentTeacher;  // 当前登录教师（用于返回主菜单）

    // 构造函数传入当前登录教师对象
    public TeacherProjectResultViewer(Teacher teacher) {
        this.currentTeacher = teacher;

        setTitle("查询教师参与的项目与成果排名");
        setSize(1000, 450);
        setLocationRelativeTo(null);
        setDefaultCloseOperation(EXIT_ON_CLOSE);
        setLayout(new BorderLayout());

        // 顶部：输入和查询按钮
        JPanel topPanel = new JPanel();
        topPanel.add(new JLabel("请输入教师ID："));
        teacherIdField = new JTextField(10);
        topPanel.add(teacherIdField);
        queryBtn = new JButton("查询");
        topPanel.add(queryBtn);
        add(topPanel, BorderLayout.NORTH);

        // 中部：结果表格
        model = new DefaultTableModel(new String[]{
                "项目ID", "项目名称", "角色", "成果ID", "成果类别", "级别", "等级", "排名"
        }, 0);
        table = new JTable(model);
        add(new JScrollPane(table), BorderLayout.CENTER);

        // 底部：返回按钮
        JPanel bottomPanel = new JPanel();
        backBtn = new JButton("返回主菜单");
        bottomPanel.add(backBtn);
        add(bottomPanel, BorderLayout.SOUTH);

        // 按钮功能绑定
        queryBtn.addActionListener(e -> queryTeacherProjects());
        backBtn.addActionListener(e -> {
            this.dispose();  // 关闭当前窗口
            new MainDashboard(currentTeacher).setVisible(true);  // 跳转主菜单
        });
    }

    private void queryTeacherProjects() {
        model.setRowCount(0);
        String input = teacherIdField.getText().trim();
        if (input.isEmpty()) {
            JOptionPane.showMessageDialog(this, "请输入教师ID", "提示", JOptionPane.WARNING_MESSAGE);
            return;
        }

        String sql =
                "SELECT " +
                        "p.project_id, p.project_name, pp.role, " +
                        "a.achievement_id, a.category, a.level, a.grade, " +
                        "ar.ranking " +
                        "FROM ProjectParticipation pp " +
                        "JOIN Project p ON pp.project_id = p.project_id " +
                        "LEFT JOIN Achievement a ON a.project_id = p.project_id " +
                        "LEFT JOIN AchievementRanking ar ON ar.achievement_id = a.achievement_id AND ar.teacher_id = pp.teacher_id " +
                        "WHERE pp.teacher_id = ? " +
                        "ORDER BY p.project_id";

        try (Connection conn = DBUtil.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setInt(1, Integer.parseInt(input));
            ResultSet rs = stmt.executeQuery();
            boolean hasData = false;
            while (rs.next()) {
                hasData = true;
                model.addRow(new Object[]{
                        rs.getInt("project_id"),
                        rs.getString("project_name"),
                        rs.getString("role"),
                        rs.getObject("achievement_id"),
                        rs.getString("category"),
                        rs.getString("level"),
                        rs.getObject("grade"),
                        rs.getObject("ranking")
                });
            }
            if (!hasData) {
                JOptionPane.showMessageDialog(this, "未找到该教师参与的项目记录。", "提示", JOptionPane.INFORMATION_MESSAGE);
            }
        } catch (SQLException | NumberFormatException e) {
            JOptionPane.showMessageDialog(this, "查询失败: " + e.getMessage(), "错误", JOptionPane.ERROR_MESSAGE);
        }
    }

    public static void main(String[] args) {
        // 测试用 - 用一个模拟 Teacher 实例
        Teacher fakeTeacher = new Teacher(); // 你需要根据系统实际构造或登录后获取的对象
        SwingUtilities.invokeLater(() -> new TeacherProjectResultViewer(fakeTeacher).setVisible(true));
    }
}
