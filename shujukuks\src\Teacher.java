
public class Teacher {

  private long teacherId;
  private String name;
  private String gender;
  private java.sql.Date birthDate;
  private String ethnicity;
  private String education;
  private java.sql.Date startWorkDate;
  private String title;
  private double baseSalary;
  private double postSalary;
  private double bonusSalary;
  private long deptId;
  private String password;

  public Teacher() {
  }

  private String username;


  public long getTeacherId() {
    return teacherId;
  }

  public void setTeacherId(long teacherId) {
    this.teacherId = teacherId;
  }


  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }


  public String getGender() {
    return gender;
  }

  public void setGender(String gender) {
    this.gender = gender;
  }


  public java.sql.Date getBirthDate() {
    return birthDate;
  }

  public void setBirthDate(java.sql.Date birthDate) {
    this.birthDate = birthDate;
  }


  public String getEthnicity() {
    return ethnicity;
  }

  public void setEthnicity(String ethnicity) {
    this.ethnicity = ethnicity;
  }


  public String getEducation() {
    return education;
  }

  public void setEducation(String education) {
    this.education = education;
  }


  public java.sql.Date getStartWorkDate() {
    return startWorkDate;
  }

  public void setStartWorkDate(java.sql.Date startWorkDate) {
    this.startWorkDate = startWorkDate;
  }


  public String getTitle() {
    return title;
  }

  public void setTitle(String title) {
    this.title = title;
  }


  public double getBaseSalary() {
    return baseSalary;
  }

  public void setBaseSalary(double baseSalary) {
    this.baseSalary = baseSalary;
  }


  public double getPostSalary() {
    return postSalary;
  }

  public void setPostSalary(double postSalary) {
    this.postSalary = postSalary;
  }


  public double getBonusSalary() {
    return bonusSalary;
  }

  public void setBonusSalary(double bonusSalary) {
    this.bonusSalary = bonusSalary;
  }


  public long getDeptId() {
    return deptId;
  }

  public void setDeptId(long deptId) {
    this.deptId = deptId;
  }


  public String getPassword() {
    return password;
  }

  public void setPassword(String password) {
    this.password = password;
  }


  public String getUsername() {
    return username;
  }

  public void setUsername(String username) {
    this.username = username;
  }

}
