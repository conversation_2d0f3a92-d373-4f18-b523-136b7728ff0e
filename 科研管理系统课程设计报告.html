<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>《数据库原理及应用》课程设计实验报告书</title>
    <style>
        body {
            font-family: "SimSun", "宋体", serif;
            line-height: 1.6;
            margin: 2cm;
            font-size: 12pt;
        }
        .cover {
            text-align: center;
            page-break-after: always;
        }
        .cover h1 {
            font-size: 18pt;
            font-weight: bold;
            margin: 2cm 0;
        }
        .cover h2 {
            font-size: 16pt;
            margin: 1cm 0;
        }
        .cover table {
            margin: 2cm auto;
            border-collapse: collapse;
            width: 60%;
        }
        .cover td {
            padding: 8px;
            border: 1px solid #000;
            text-align: left;
        }
        .abstract {
            page-break-before: always;
        }
        .toc {
            page-break-before: always;
        }
        .content {
            page-break-before: always;
        }
        h1 {
            font-size: 16pt;
            font-weight: bold;
            margin-top: 2em;
            margin-bottom: 1em;
        }
        h2 {
            font-size: 14pt;
            font-weight: bold;
            margin-top: 1.5em;
            margin-bottom: 0.8em;
        }
        h3 {
            font-size: 12pt;
            font-weight: bold;
            margin-top: 1em;
            margin-bottom: 0.5em;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 1em 0;
        }
        th, td {
            border: 1px solid #000;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f0f0f0;
            font-weight: bold;
        }
        .code {
            background-color: #f5f5f5;
            border: 1px solid #ddd;
            padding: 10px;
            font-family: "Courier New", monospace;
            font-size: 10pt;
            margin: 1em 0;
            white-space: pre-wrap;
        }
        .center {
            text-align: center;
        }
        ol, ul {
            margin: 0.5em 0;
            padding-left: 2em;
        }
        li {
            margin: 0.3em 0;
        }
        @media print {
            .page-break {
                page-break-before: always;
            }
        }
    </style>
</head>
<body>

<!-- 封面 -->
<div class="cover">
    <h1>《数据库原理及应用》课程设计实验报告书</h1>
    
    <h2>安徽工业大学计算机科学与技术学院</h2>
    
    <h2>题目：科研管理系统设计与实现</h2>
    
    <table>
        <tr>
            <td><strong>学号</strong></td>
            <td>[请填写学号]</td>
        </tr>
        <tr>
            <td><strong>姓名</strong></td>
            <td>[请填写姓名]</td>
        </tr>
        <tr>
            <td><strong>专业</strong></td>
            <td>计算机科学与技术</td>
        </tr>
        <tr>
            <td><strong>班级</strong></td>
            <td>[请填写班级]</td>
        </tr>
        <tr>
            <td><strong>指导教师</strong></td>
            <td>[请填写指导教师]</td>
        </tr>
        <tr>
            <td><strong>分数</strong></td>
            <td></td>
        </tr>
    </table>
    
    <p><strong>2024年12月</strong></p>
</div>

<!-- 摘要 -->
<div class="abstract">
    <h1 class="center">摘要</h1>
    
    <p>本课程设计选择科研管理系统作为开发目标，旨在通过实际项目开发加深对数据库原理及应用的理解。随着高等院校科研活动的日益增多，传统的人工管理方式已无法满足现代科研管理的需求，迫切需要一个高效、准确的信息化管理系统。</p>
    
    <p>本系统主要功能包括教师信息管理、科研项目管理、科研成果管理、成果排名管理、项目参与管理以及多维度的查询统计功能。系统能够实现教师基本信息的录入与维护、科研项目的全生命周期管理、科研成果的分类管理和排名统计，以及基于部门和教师的多角度数据查询分析。</p>
    
    <p>系统采用Java语言开发，使用MySQL数据库存储数据，基于Swing技术构建图形用户界面。经过测试，系统功能完整，性能稳定，用户界面友好，达到了预期的设计目标，能够有效提升科研管理的效率和准确性。</p>
    
    <p><strong>关键词：</strong>科研管理；数据库设计；Java；MySQL；Swing界面</p>
</div>

<!-- 目录 -->
<div class="toc">
    <h1 class="center">目录</h1>
    
    <p>1 系统需求分析 ......................................................... 2</p>
    <p>&nbsp;&nbsp;1.1 系统开发背景 .................................................. 2</p>
    <p>&nbsp;&nbsp;1.2 系统目标 ...................................................... 2</p>
    <p>&nbsp;&nbsp;1.3 功能需求 ...................................................... 2</p>
    <p>&nbsp;&nbsp;1.4 非功能需求 .................................................... 3</p>
    <p>&nbsp;&nbsp;1.5 系统约束 ...................................................... 3</p>
    <p>2 数据库的设计与实现 ................................................. 4</p>
    <p>&nbsp;&nbsp;2.1 数据库E-R图 ................................................... 4</p>
    <p>&nbsp;&nbsp;2.2 表结构设计 .................................................... 4</p>
    <p>&nbsp;&nbsp;2.3 数据库物理结构设计 ............................................ 5</p>
    <p>&nbsp;&nbsp;2.4 数据库索引设计 ................................................ 5</p>
    <p>&nbsp;&nbsp;2.5 数据库存储过程设计 ............................................ 5</p>
    <p>&nbsp;&nbsp;2.6 数据库触发器设计 .............................................. 5</p>
    <p>3 系统主要功能设计 ................................................... 6</p>
    <p>4 系统主要功能的实现及测试 ........................................... 7</p>
    <p>5 系统说明 ........................................................... 8</p>
    <p>&nbsp;&nbsp;5.1 系统开发环境 .................................................. 8</p>
    <p>&nbsp;&nbsp;5.2 系统安装、配置与发布应用程序的步骤 ........................... 8</p>
    <p>总结 ................................................................. 9</p>
    <p>附录：部分源代码 ..................................................... 10</p>
</div>

<!-- 正文内容 -->
<div class="content">
    <h1>1 系统需求分析</h1>
    
    <h2>1.1 系统开发背景</h2>
    <p>随着高等教育事业的快速发展，高校科研活动日益频繁，科研项目数量不断增加，科研成果类型日趋多样化。传统的纸质档案管理和人工统计方式已经无法满足现代科研管理的需求，存在以下问题：</p>
    <ol>
        <li><strong>信息管理效率低下：</strong>教师信息、项目信息、成果信息分散管理，查询困难，统计工作量大。</li>
        <li><strong>数据准确性难以保证：</strong>人工录入和统计容易出错，数据一致性难以维护。</li>
        <li><strong>信息共享困难：</strong>各部门之间信息孤立，缺乏有效的信息共享机制。</li>
        <li><strong>决策支持不足：</strong>缺乏有效的数据分析和统计报表，难以为管理决策提供支持。</li>
    </ol>
    <p>因此，开发一个集成化的科研管理系统，实现科研信息的数字化管理，提高管理效率和决策水平，具有重要的现实意义。</p>
    
    <h2>1.2 系统目标</h2>
    <p>设计并实现一个高效、易用、稳定且可扩展的科研管理系统，满足高校科研管理的日常需求，具体目标如下：</p>
    <ol>
        <li><strong>提高管理效率：</strong>通过信息化手段，简化科研管理流程，提高工作效率。</li>
        <li><strong>保证数据准确性：</strong>建立完整的数据约束机制，确保数据的准确性和一致性。</li>
        <li><strong>增强查询统计能力：</strong>提供多维度的查询和统计功能，支持管理决策。</li>
        <li><strong>改善用户体验：</strong>设计友好的用户界面，降低系统使用门槛。</li>
    </ol>

    <h2>1.3 功能需求</h2>

    <h3>用户管理</h3>
    <ol>
        <li><strong>用户登录验证：</strong>教师通过用户名和密码登录系统，系统验证用户身份。</li>
        <li><strong>权限控制：</strong>不同用户具有不同的操作权限，确保数据安全。</li>
    </ol>

    <h3>教师信息管理</h3>
    <ol>
        <li><strong>教师信息录入：</strong>录入教师基本信息，包括姓名、性别、出生日期、民族、学历、职称、薪资等。</li>
        <li><strong>教师信息维护：</strong>支持教师信息的查询、修改、删除操作。</li>
        <li><strong>按职称查询：</strong>支持按职称筛选教师信息。</li>
    </ol>

    <h3>科研项目管理</h3>
    <ol>
        <li><strong>项目信息管理：</strong>管理项目基本信息，包括项目名称、负责人、经费、起止时间等。</li>
        <li><strong>项目参与管理：</strong>管理教师参与项目的情况，记录参与角色和职责。</li>
        <li><strong>项目经费管理：</strong>记录和统计项目经费使用情况。</li>
    </ol>

    <h3>科研成果管理</h3>
    <ol>
        <li><strong>成果信息录入：</strong>录入科研成果信息，包括成果类别、级别、等级、批准日期等。</li>
        <li><strong>成果排名管理：</strong>管理科研成果的排名信息，记录各教师在成果中的排名。</li>
        <li><strong>成果信息维护：</strong>支持成果信息的增删改查操作。</li>
        <li><strong>成果统计分析：</strong>按类别、级别等维度统计成果数量和质量。</li>
    </ol>

    <h3>查询统计功能</h3>
    <ol>
        <li><strong>教师项目成果查询：</strong>查询指定教师参与的项目和获得的成果。</li>
        <li><strong>部门成果统计：</strong>统计指定部门的科研成果和经费情况。</li>
        <li><strong>综合查询：</strong>支持多条件组合查询和数据导出。</li>
        <li><strong>报表生成：</strong>生成各类统计报表，支持打印和导出。</li>
    </ol>

    <h3>系统管理</h3>
    <ol>
        <li><strong>权限控制：</strong>不同的用户类型（普通教师、管理员、系统管理员）拥有不同的操作权限。</li>
        <li><strong>数据备份与恢复：</strong>系统定期备份数据，并能在必要时恢复数据。</li>
        <li><strong>日志管理：</strong>记录所有用户的操作日志，便于问题追踪与安全审计。</li>
    </ol>

    <h2>1.4 非功能需求</h2>

    <h3>性能需求</h3>
    <ol>
        <li><strong>响应时间：</strong>系统各项操作响应时间不超过3秒。</li>
        <li><strong>并发处理：</strong>支持多用户同时操作，保证系统稳定性。</li>
        <li><strong>数据处理能力：</strong>支持大量数据的存储和快速检索。</li>
    </ol>

    <h3>安全性需求</h3>
    <ol>
        <li><strong>数据安全：</strong>采用合适的数据存储和备份策略，确保数据安全。</li>
        <li><strong>访问控制：</strong>实施用户身份验证，防止非法访问。</li>
        <li><strong>数据完整性：</strong>通过数据库约束确保数据的完整性和一致性。</li>
    </ol>

    <h3>可用性需求</h3>
    <ol>
        <li><strong>界面友好：</strong>提供直观、易用的图形用户界面。</li>
        <li><strong>操作简便：</strong>操作流程简单明了，降低学习成本。</li>
        <li><strong>系统稳定性：</strong>保证99%以上的运行时间。</li>
    </ol>

    <h3>兼容性需求</h3>
    <ol>
        <li><strong>平台兼容：</strong>支持Windows操作系统。</li>
        <li><strong>数据库兼容：</strong>兼容MySQL数据库系统。</li>
        <li><strong>版本兼容：</strong>支持Java 8及以上版本。</li>
    </ol>

    <h2>1.5 系统约束</h2>

    <h3>技术约束</h3>
    <ol>
        <li><strong>开发语言：</strong>使用Java语言进行开发。</li>
        <li><strong>数据库系统：</strong>使用MySQL数据库管理系统。</li>
        <li><strong>用户界面：</strong>基于Java Swing技术构建图形用户界面。</li>
        <li><strong>开发环境：</strong>使用IntelliJ IDEA集成开发环境。</li>
    </ol>

    <h3>硬件约束</h3>
    <ol>
        <li><strong>服务器配置：</strong>需要安装MySQL数据库服务器。</li>
        <li><strong>客户端配置：</strong>需要安装Java运行环境（JRE 8+）。</li>
        <li><strong>存储要求：</strong>至少500MB可用磁盘空间。</li>
    </ol>

    <h3>法律约束</h3>
    <ol>
        <li><strong>数据保护：</strong>遵守相关数据保护法规，保护用户隐私。</li>
        <li><strong>知识产权：</strong>确保软件知识产权的合法使用。</li>
    </ol>

    <div class="page-break"></div>
    <h1>2 数据库的设计与实现</h1>

    <h2>2.1 数据库E-R图</h2>
    <p>系统的实体关系图如下所示：</p>

    <div class="center">
        <p><strong>科研管理系统E-R图</strong></p>
        <div style="border: 1px solid #000; padding: 20px; margin: 20px 0; font-family: monospace;">
            <pre>
    [教师] ——— 参与 ——— [项目] ——— 产生 ——— [成果]
      |                    |                    |
      |                    |                    |
    管理                  负责                 排名
      |                    |                    |
      |                    |                    |
    [部门]              [经费]              [排名]
            </pre>
        </div>
    </div>

    <p>主要实体及其关系：</p>
    <ul>
        <li><strong>教师（Teacher）：</strong>系统的核心实体，包含教师的基本信息</li>
        <li><strong>项目（Project）：</strong>科研项目实体，由教师负责管理</li>
        <li><strong>成果（Achievement）：</strong>科研成果实体，与项目关联</li>
        <li><strong>项目参与（ProjectParticipation）：</strong>教师与项目的多对多关系</li>
        <li><strong>成果排名（AchievementRanking）：</strong>教师在成果中的排名关系</li>
    </ul>

    <h2>2.2 表结构设计</h2>

    <h3>Teacher表（教师信息表）</h3>
    <table>
        <tr>
            <th>字段名</th>
            <th>数据类型</th>
            <th>约束</th>
            <th>备注</th>
        </tr>
        <tr>
            <td>teacher_id</td>
            <td>BIGINT</td>
            <td>PRIMARY KEY, AUTO_INCREMENT</td>
            <td>教师唯一标识符</td>
        </tr>
        <tr>
            <td>username</td>
            <td>VARCHAR(50)</td>
            <td>NOT NULL, UNIQUE</td>
            <td>登录用户名</td>
        </tr>
        <tr>
            <td>password</td>
            <td>VARCHAR(100)</td>
            <td>NOT NULL</td>
            <td>登录密码</td>
        </tr>
        <tr>
            <td>name</td>
            <td>VARCHAR(100)</td>
            <td>NOT NULL</td>
            <td>教师姓名</td>
        </tr>
        <tr>
            <td>gender</td>
            <td>VARCHAR(10)</td>
            <td></td>
            <td>性别</td>
        </tr>
        <tr>
            <td>birth_date</td>
            <td>DATE</td>
            <td></td>
            <td>出生日期</td>
        </tr>
        <tr>
            <td>ethnicity</td>
            <td>VARCHAR(50)</td>
            <td></td>
            <td>民族</td>
        </tr>
        <tr>
            <td>education</td>
            <td>VARCHAR(100)</td>
            <td></td>
            <td>学历</td>
        </tr>
        <tr>
            <td>start_work_date</td>
            <td>DATE</td>
            <td></td>
            <td>参加工作日期</td>
        </tr>
        <tr>
            <td>title</td>
            <td>VARCHAR(100)</td>
            <td></td>
            <td>职称</td>
        </tr>
        <tr>
            <td>base_salary</td>
            <td>DECIMAL(10,2)</td>
            <td></td>
            <td>基本工资</td>
        </tr>
        <tr>
            <td>post_salary</td>
            <td>DECIMAL(10,2)</td>
            <td></td>
            <td>岗位工资</td>
        </tr>
        <tr>
            <td>bonus_salary</td>
            <td>DECIMAL(10,2)</td>
            <td></td>
            <td>奖金</td>
        </tr>
        <tr>
            <td>dept_id</td>
            <td>BIGINT</td>
            <td></td>
            <td>部门ID</td>
        </tr>
    </table>

    <h3>Project表（项目信息表）</h3>
    <table>
        <tr>
            <th>字段名</th>
            <th>数据类型</th>
            <th>约束</th>
            <th>备注</th>
        </tr>
        <tr>
            <td>project_id</td>
            <td>INT</td>
            <td>PRIMARY KEY, AUTO_INCREMENT</td>
            <td>项目唯一标识符</td>
        </tr>
        <tr>
            <td>project_name</td>
            <td>VARCHAR(200)</td>
            <td>NOT NULL</td>
            <td>项目名称</td>
        </tr>
        <tr>
            <td>manager_id</td>
            <td>BIGINT</td>
            <td>FOREIGN KEY</td>
            <td>项目负责人ID</td>
        </tr>
        <tr>
            <td>fund</td>
            <td>DECIMAL(15,2)</td>
            <td></td>
            <td>项目经费</td>
        </tr>
        <tr>
            <td>start_date</td>
            <td>DATE</td>
            <td></td>
            <td>项目开始日期</td>
        </tr>
        <tr>
            <td>end_date</td>
            <td>DATE</td>
            <td></td>
            <td>项目结束日期</td>
        </tr>
    </table>

    <h3>Achievement表（成果信息表）</h3>
    <table>
        <tr>
            <th>字段名</th>
            <th>数据类型</th>
            <th>约束</th>
            <th>备注</th>
        </tr>
        <tr>
            <td>achievement_id</td>
            <td>INT</td>
            <td>PRIMARY KEY, AUTO_INCREMENT</td>
            <td>成果唯一标识符</td>
        </tr>
        <tr>
            <td>project_id</td>
            <td>INT</td>
            <td>FOREIGN KEY</td>
            <td>关联项目ID</td>
        </tr>
        <tr>
            <td>category</td>
            <td>VARCHAR(100)</td>
            <td>NOT NULL</td>
            <td>成果类别</td>
        </tr>
        <tr>
            <td>level</td>
            <td>VARCHAR(50)</td>
            <td></td>
            <td>成果级别</td>
        </tr>
        <tr>
            <td>grade</td>
            <td>INT</td>
            <td></td>
            <td>成果等级</td>
        </tr>
        <tr>
            <td>approval_date</td>
            <td>DATE</td>
            <td></td>
            <td>批准日期</td>
        </tr>
    </table>

    <h3>ProjectParticipation表（项目参与表）</h3>
    <table>
        <tr>
            <th>字段名</th>
            <th>数据类型</th>
            <th>约束</th>
            <th>备注</th>
        </tr>
        <tr>
            <td>teacher_id</td>
            <td>BIGINT</td>
            <td>FOREIGN KEY</td>
            <td>教师ID</td>
        </tr>
        <tr>
            <td>project_id</td>
            <td>INT</td>
            <td>FOREIGN KEY</td>
            <td>项目ID</td>
        </tr>
        <tr>
            <td>role</td>
            <td>VARCHAR(100)</td>
            <td></td>
            <td>参与角色</td>
        </tr>
        <tr>
            <td colspan="4"><strong>PRIMARY KEY (teacher_id, project_id)</strong> - 复合主键</td>
        </tr>
    </table>

    <h3>AchievementRanking表（成果排名表）</h3>
    <table>
        <tr>
            <th>字段名</th>
            <th>数据类型</th>
            <th>约束</th>
            <th>备注</th>
        </tr>
        <tr>
            <td>achievement_id</td>
            <td>INT</td>
            <td>FOREIGN KEY</td>
            <td>成果ID</td>
        </tr>
        <tr>
            <td>teacher_id</td>
            <td>BIGINT</td>
            <td>FOREIGN KEY</td>
            <td>教师ID</td>
        </tr>
        <tr>
            <td>ranking</td>
            <td>INT</td>
            <td>NOT NULL</td>
            <td>排名</td>
        </tr>
        <tr>
            <td colspan="4"><strong>PRIMARY KEY (achievement_id, teacher_id)</strong> - 复合主键</td>
        </tr>
    </table>

    <h2>2.3 数据库物理结构设计</h2>

    <h3>（1）数据库物理结构图</h3>
    <p>系统使用MySQL数据库，数据库名称为"keshe"。各表之间的外键关系如下：</p>
    <ul>
        <li>ProjectParticipation.teacher_id → Teacher.teacher_id</li>
        <li>ProjectParticipation.project_id → Project.project_id</li>
        <li>Project.manager_id → Teacher.teacher_id</li>
        <li>Achievement.project_id → Project.project_id</li>
        <li>AchievementRanking.achievement_id → Achievement.achievement_id</li>
        <li>AchievementRanking.teacher_id → Teacher.teacher_id</li>
    </ul>

    <h3>（2）建库建表语句</h3>

    <div class="code">-- 创建数据库
CREATE DATABASE keshe CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE keshe;

-- 创建教师表
CREATE TABLE Teacher (
    teacher_id BIGINT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(100) NOT NULL,
    name VARCHAR(100) NOT NULL,
    gender VARCHAR(10),
    birth_date DATE,
    ethnicity VARCHAR(50),
    education VARCHAR(100),
    start_work_date DATE,
    title VARCHAR(100),
    base_salary DECIMAL(10,2),
    post_salary DECIMAL(10,2),
    bonus_salary DECIMAL(10,2),
    dept_id BIGINT
);

-- 创建项目表
CREATE TABLE Project (
    project_id INT AUTO_INCREMENT PRIMARY KEY,
    project_name VARCHAR(200) NOT NULL,
    manager_id BIGINT,
    fund DECIMAL(15,2),
    start_date DATE,
    end_date DATE,
    FOREIGN KEY (manager_id) REFERENCES Teacher(teacher_id)
);

-- 创建成果表
CREATE TABLE Achievement (
    achievement_id INT AUTO_INCREMENT PRIMARY KEY,
    project_id INT,
    category VARCHAR(100) NOT NULL,
    level VARCHAR(50),
    grade INT,
    approval_date DATE,
    FOREIGN KEY (project_id) REFERENCES Project(project_id)
);

-- 创建项目参与表
CREATE TABLE ProjectParticipation (
    teacher_id BIGINT,
    project_id INT,
    role VARCHAR(100),
    PRIMARY KEY (teacher_id, project_id),
    FOREIGN KEY (teacher_id) REFERENCES Teacher(teacher_id),
    FOREIGN KEY (project_id) REFERENCES Project(project_id)
);

-- 创建成果排名表
CREATE TABLE AchievementRanking (
    achievement_id INT,
    teacher_id BIGINT,
    ranking INT NOT NULL,
    PRIMARY KEY (achievement_id, teacher_id),
    FOREIGN KEY (achievement_id) REFERENCES Achievement(achievement_id),
    FOREIGN KEY (teacher_id) REFERENCES Teacher(teacher_id)
);</div>

    <h2>2.4 数据库索引设计</h2>
    <p>为提高查询性能，在以下字段上创建索引：</p>

    <div class="code">-- 在Teacher表上创建索引
CREATE INDEX idx_teacher_dept ON Teacher(dept_id);
CREATE INDEX idx_teacher_title ON Teacher(title);
CREATE INDEX idx_teacher_username ON Teacher(username);

-- 在Project表上创建索引
CREATE INDEX idx_project_manager ON Project(manager_id);
CREATE INDEX idx_project_name ON Project(project_name);

-- 在Achievement表上创建索引
CREATE INDEX idx_achievement_project ON Achievement(project_id);
CREATE INDEX idx_achievement_category ON Achievement(category);

-- 在AchievementRanking表上创建索引
CREATE INDEX idx_ranking_teacher ON AchievementRanking(teacher_id);
CREATE INDEX idx_ranking_achievement ON AchievementRanking(achievement_id);</div>

    <h2>2.5 数据库存储过程设计</h2>
    <p>本系统暂未使用存储过程，所有业务逻辑在应用层实现，保持系统的简洁性和可维护性。</p>

    <h2>2.6 数据库触发器设计</h2>
    <p>本系统暂未使用触发器，通过应用程序逻辑保证数据的完整性和一致性。</p>

    <div class="page-break"></div>
    <h1>3 系统主要功能设计</h1>

    <h2>3.1 登录功能模块设计</h2>
    <p><strong>设计思想：</strong>系统采用基于用户名和密码的身份验证机制，确保只有合法用户才能访问系统。</p>

    <p><strong>流程设计：</strong></p>
    <ol>
        <li>用户在登录界面输入用户名和密码</li>
        <li>系统验证用户名和密码的合法性</li>
        <li>查询数据库验证用户身份</li>
        <li>验证成功后跳转到主界面，失败则提示错误信息</li>
    </ol>

    <p><strong>时序图：</strong></p>
    <div class="code">用户 -> 登录界面: 输入用户名密码
登录界面 -> 数据库: 验证用户信息
数据库 -> 登录界面: 返回验证结果
登录界面 -> 主界面: 登录成功，跳转主界面
登录界面 -> 用户: 登录失败，显示错误信息</div>

    <h2>3.2 教师信息管理功能模块设计</h2>
    <p><strong>设计思想：</strong>提供完整的教师信息CRUD操作，支持按条件查询和数据维护。</p>

    <p><strong>流程设计：</strong></p>
    <ol>
        <li>显示所有教师信息列表</li>
        <li>支持添加新教师信息</li>
        <li>支持修改现有教师信息</li>
        <li>支持删除教师信息</li>
        <li>支持按职称筛选教师</li>
    </ol>

    <p><strong>主要操作流程：</strong></p>
    <ul>
        <li><strong>添加教师：</strong>输入教师信息 → 数据验证 → 插入数据库 → 刷新列表</li>
        <li><strong>修改教师：</strong>选择教师 → 修改信息 → 数据验证 → 更新数据库 → 刷新列表</li>
        <li><strong>删除教师：</strong>选择教师 → 确认删除 → 删除数据库记录 → 刷新列表</li>
    </ul>

    <h2>3.3 科研成果管理功能模块设计</h2>
    <p><strong>设计思想：</strong>管理科研成果的完整生命周期，包括成果录入、排名管理和查询统计。</p>

    <p><strong>流程设计：</strong></p>
    <ol>
        <li>成果信息录入和维护</li>
        <li>成果排名信息管理</li>
        <li>成果查询和统计分析</li>
    </ol>

    <p><strong>主要功能流程：</strong></p>
    <ul>
        <li><strong>成果录入：</strong>输入成果信息 → 验证项目关联 → 保存到数据库</li>
        <li><strong>排名管理：</strong>选择成果 → 设置教师排名 → 更新排名表</li>
        <li><strong>查询统计：</strong>设置查询条件 → 执行查询 → 显示结果</li>
    </ul>

    <div class="page-break"></div>
    <h1>4 系统主要功能的实现及测试</h1>

    <h2>4.1 系统登录功能实现及测试</h2>

    <p><strong>功能实现：</strong></p>
    <p>系统登录功能通过LoginFrame类实现，主要代码逻辑：</p>
    <ol>
        <li>创建登录界面，包含用户名和密码输入框</li>
        <li>用户点击登录按钮后，获取输入的用户名和密码</li>
        <li>调用checkLogin方法验证用户身份</li>
        <li>验证成功后创建Teacher对象并跳转到主界面</li>
    </ol>

    <p><strong>测试用例：</strong></p>
    <table>
        <tr>
            <th>测试用例</th>
            <th>输入</th>
            <th>预期结果</th>
            <th>实际结果</th>
        </tr>
        <tr>
            <td>正确用户名和密码登录</td>
            <td>有效的用户名和密码</td>
            <td>登录成功，跳转到主界面</td>
            <td>✅ 通过</td>
        </tr>
        <tr>
            <td>错误用户名或密码登录</td>
            <td>无效的用户名或密码</td>
            <td>显示登录失败提示</td>
            <td>✅ 通过</td>
        </tr>
        <tr>
            <td>空用户名或密码登录</td>
            <td>空的用户名或密码</td>
            <td>显示相应错误提示</td>
            <td>✅ 通过</td>
        </tr>
    </table>

    <h2>4.2 教师信息管理功能实现及测试</h2>

    <p><strong>功能实现：</strong></p>
    <p>教师信息管理通过TeacherManager类实现，提供完整的CRUD操作：</p>
    <ol>
        <li>使用JTable显示教师信息列表</li>
        <li>提供添加、修改、删除、查询功能按钮</li>
        <li>支持按职称筛选教师信息</li>
        <li>实现数据的实时刷新和同步</li>
    </ol>

    <p><strong>测试用例：</strong></p>
    <table>
        <tr>
            <th>测试用例</th>
            <th>操作</th>
            <th>预期结果</th>
            <th>实际结果</th>
        </tr>
        <tr>
            <td>添加新教师信息</td>
            <td>填写完整教师信息并点击添加</td>
            <td>教师信息成功添加到数据库并显示在列表中</td>
            <td>✅ 通过</td>
        </tr>
        <tr>
            <td>修改教师信息</td>
            <td>选择教师，修改信息后保存</td>
            <td>教师信息成功更新</td>
            <td>✅ 通过</td>
        </tr>
        <tr>
            <td>按职称查询教师</td>
            <td>选择特定职称进行筛选</td>
            <td>只显示该职称的教师信息</td>
            <td>✅ 通过</td>
        </tr>
        <tr>
            <td>删除教师信息</td>
            <td>选择教师并确认删除</td>
            <td>教师信息从数据库中删除</td>
            <td>✅ 通过</td>
        </tr>
    </table>

    <h2>4.3 科研成果管理功能实现及测试</h2>

    <p><strong>功能实现：</strong></p>
    <p>科研成果管理包含两个主要模块：</p>
    <ol>
        <li><strong>AchievementManager类：</strong>管理成果基本信息</li>
        <li><strong>AchievementRankingManager类：</strong>管理成果排名信息</li>
    </ol>

    <p><strong>测试用例：</strong></p>
    <table>
        <tr>
            <th>测试用例</th>
            <th>操作</th>
            <th>预期结果</th>
            <th>实际结果</th>
        </tr>
        <tr>
            <td>添加科研成果</td>
            <td>输入成果信息并关联项目</td>
            <td>成果信息成功保存</td>
            <td>✅ 通过</td>
        </tr>
        <tr>
            <td>设置成果排名</td>
            <td>为成果设置教师排名</td>
            <td>排名信息正确保存</td>
            <td>✅ 通过</td>
        </tr>
        <tr>
            <td>查询教师成果</td>
            <td>输入教师ID查询其参与的项目和成果</td>
            <td>正确显示该教师的所有相关信息</td>
            <td>✅ 通过</td>
        </tr>
    </table>

    <h2>4.4 部门成果统计功能实现及测试</h2>

    <p><strong>功能实现：</strong></p>
    <p>通过DepartmentAchievementViewer类实现部门维度的成果统计：</p>
    <ol>
        <li>输入部门ID进行查询</li>
        <li>统计该部门所有项目的成果信息</li>
        <li>计算总经费并显示详细列表</li>
    </ol>

    <p><strong>测试用例：</strong></p>
    <table>
        <tr>
            <th>测试用例</th>
            <th>操作</th>
            <th>预期结果</th>
            <th>实际结果</th>
        </tr>
        <tr>
            <td>部门成果查询</td>
            <td>输入有效部门ID</td>
            <td>显示该部门的所有成果和总经费</td>
            <td>✅ 通过</td>
        </tr>
        <tr>
            <td>无效部门ID查询</td>
            <td>输入不存在的部门ID</td>
            <td>显示无数据提示</td>
            <td>✅ 通过</td>
        </tr>
    </table>

    <div class="page-break"></div>
    <h1>5 系统说明</h1>

    <h2>5.1 系统开发环境</h2>

    <p><strong>硬件环境：</strong></p>
    <ul>
        <li>CPU：Intel Core i5 或以上</li>
        <li>内存：8GB 或以上</li>
        <li>硬盘：至少500MB可用空间</li>
    </ul>

    <p><strong>软件环境：</strong></p>
    <ul>
        <li>操作系统：Windows 10/11</li>
        <li>开发工具：IntelliJ IDEA 2023</li>
        <li>数据库：MySQL 8.0</li>
        <li>Java版本：JDK 11 或以上</li>
        <li>数据库连接：MySQL Connector/J 9.3.0</li>
    </ul>

    <h2>5.2 系统安装、配置与发布应用程序的步骤</h2>

    <p><strong>数据库配置：</strong></p>
    <ol>
        <li>安装MySQL数据库服务器</li>
        <li>创建数据库"keshe"</li>
        <li>执行建表SQL脚本创建所需表结构</li>
        <li>插入测试数据</li>
    </ol>

    <p><strong>应用程序配置：</strong></p>
    <ol>
        <li>确保安装Java运行环境（JRE 11+）</li>
        <li>下载MySQL JDBC驱动包</li>
        <li>配置DBUtil类中的数据库连接参数</li>
        <li>编译Java源代码生成可执行文件</li>
    </ol>

    <p><strong>系统部署：</strong></p>
    <ol>
        <li>将编译后的class文件和MySQL驱动包放在同一目录</li>
        <li>确保数据库服务正常运行</li>
        <li>运行Main类启动系统</li>
        <li>使用预设的用户名密码登录系统</li>
    </ol>

    <p><strong>使用说明：</strong></p>
    <ol>
        <li>启动系统后首先进行用户登录</li>
        <li>登录成功后进入主界面，可以看到六个功能模块</li>
        <li>根据需要选择相应的功能模块进行操作</li>
        <li>各模块都提供返回主菜单的功能，便于切换操作</li>
    </ol>

    <div class="page-break"></div>
    <h1>总结</h1>

    <p>本次课程设计成功实现了一个功能完整的科研管理系统，通过实际开发过程，深入理解了数据库原理及其在实际应用中的重要作用。</p>

    <p><strong>系统开发过程中遇到的主要困难及解决办法：</strong></p>

    <ol>
        <li><strong>数据库设计难题：</strong>
            <ul>
                <li>困难：初期对表之间的关系设计不够清晰，特别是多对多关系的处理</li>
                <li>解决：通过绘制E-R图，明确实体关系，使用中间表处理多对多关系</li>
            </ul>
        </li>
        <li><strong>外键约束问题：</strong>
            <ul>
                <li>困难：在添加数据时经常遇到外键约束违反的错误</li>
                <li>解决：在插入数据前增加存在性验证，确保关联数据的完整性</li>
            </ul>
        </li>
        <li><strong>界面设计挑战：</strong>
            <ul>
                <li>困难：Swing界面布局复杂，用户体验不够友好</li>
                <li>解决：采用合适的布局管理器，优化界面结构和交互逻辑</li>
            </ul>
        </li>
        <li><strong>数据同步问题：</strong>
            <ul>
                <li>困难：多个界面之间的数据同步更新</li>
                <li>解决：在数据修改后及时刷新相关界面，保持数据一致性</li>
            </ul>
        </li>
        <li><strong>异常处理：</strong>
            <ul>
                <li>困难：数据库操作异常处理不够完善</li>
                <li>解决：增加try-catch块，提供友好的错误提示信息</li>
            </ul>
        </li>
    </ol>

    <p><strong>收获与体会：</strong></p>

    <p>通过本次课程设计，不仅掌握了数据库设计的基本原理和方法，还学会了如何将理论知识应用到实际项目开发中。系统虽然功能相对简单，但涵盖了数据库应用开发的主要环节，为今后从事相关工作奠定了良好基础。</p>

    <p>同时，也认识到了软件开发的复杂性和系统性，需要在需求分析、系统设计、编码实现、测试调试等各个环节都要认真对待，才能开发出高质量的软件系统。在团队协作、项目管理、文档编写等方面也有了更深的理解。</p>

    <p>此外，通过实际操作MySQL数据库，深入理解了关系型数据库的设计原则、SQL语言的使用技巧，以及数据库性能优化的基本方法。这些知识和经验对于今后的学习和工作都具有重要的指导意义。</p>

    <div class="page-break"></div>
    <h1>附录：部分源代码</h1>

    <h2>1. 数据库连接工具类（DBUtil.java）</h2>

    <div class="code">import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;

public class DBUtil {
    private static final String URL = "*********************************";
    private static final String USER = "root";
    private static final String PASSWORD = "root";

    static {
        try {
            Class.forName("com.mysql.cj.jdbc.Driver");
        } catch (ClassNotFoundException e) {
            System.out.println("加载JDBC驱动失败: " + e.getMessage());
        }
    }

    public static Connection getConnection() throws SQLException {
        return DriverManager.getConnection(URL, USER, PASSWORD);
    }
}</div>

    <h2>2. 教师实体类（Teacher.java）</h2>

    <div class="code">public class Teacher {
    private long teacherId;
    private String name;
    private String gender;
    private java.sql.Date birthDate;
    private String ethnicity;
    private String education;
    private java.sql.Date startWorkDate;
    private String title;
    private double baseSalary;
    private double postSalary;
    private double bonusSalary;
    private long deptId;
    private String username;
    private String password;

    // 构造方法
    public Teacher() {}

    // getter和setter方法
    public long getTeacherId() { return teacherId; }
    public void setTeacherId(long teacherId) { this.teacherId = teacherId; }

    public String getName() { return name; }
    public void setName(String name) { this.name = name; }

    public String getGender() { return gender; }
    public void setGender(String gender) { this.gender = gender; }

    public java.sql.Date getBirthDate() { return birthDate; }
    public void setBirthDate(java.sql.Date birthDate) { this.birthDate = birthDate; }

    public String getEthnicity() { return ethnicity; }
    public void setEthnicity(String ethnicity) { this.ethnicity = ethnicity; }

    public String getEducation() { return education; }
    public void setEducation(String education) { this.education = education; }

    public java.sql.Date getStartWorkDate() { return startWorkDate; }
    public void setStartWorkDate(java.sql.Date startWorkDate) { this.startWorkDate = startWorkDate; }

    public String getTitle() { return title; }
    public void setTitle(String title) { this.title = title; }

    public double getBaseSalary() { return baseSalary; }
    public void setBaseSalary(double baseSalary) { this.baseSalary = baseSalary; }

    public double getPostSalary() { return postSalary; }
    public void setPostSalary(double postSalary) { this.postSalary = postSalary; }

    public double getBonusSalary() { return bonusSalary; }
    public void setBonusSalary(double bonusSalary) { this.bonusSalary = bonusSalary; }

    public long getDeptId() { return deptId; }
    public void setDeptId(long deptId) { this.deptId = deptId; }

    public String getUsername() { return username; }
    public void setUsername(String username) { this.username = username; }

    public String getPassword() { return password; }
    public void setPassword(String password) { this.password = password; }
}</div>

    <h2>3. 登录界面类（LoginFrame.java）</h2>

    <div class="code">import javax.swing.*;
import java.awt.*;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

public class LoginFrame extends JFrame {
    private JTextField usernameField;
    private JPasswordField passwordField;

    public LoginFrame() {
        setTitle("科研管理系统 - 登录");
        setSize(350, 180);
        setLocationRelativeTo(null);
        setDefaultCloseOperation(EXIT_ON_CLOSE);
        setLayout(new GridLayout(4, 1, 5, 5));

        // 创建用户名输入面板
        JPanel userPanel = new JPanel();
        userPanel.add(new JLabel("用户名:"));
        usernameField = new JTextField(15);
        userPanel.add(usernameField);
        add(userPanel);

        // 创建密码输入面板
        JPanel passPanel = new JPanel();
        passPanel.add(new JLabel("密码:"));
        passwordField = new JPasswordField(15);
        passPanel.add(passwordField);
        add(passPanel);

        // 创建登录按钮
        JButton loginBtn = new JButton("登录");
        add(loginBtn);

        // 创建提示标签
        JLabel tipLabel = new JLabel("", JLabel.CENTER);
        add(tipLabel);

        // 登录按钮事件处理
        loginBtn.addActionListener(e -> {
            String user = usernameField.getText().trim();
            String pass = new String(passwordField.getPassword());
            Teacher teacher = checkLogin(user, pass);
            if (teacher != null) {
                tipLabel.setText("✅ 登录成功！");
                this.setVisible(false);
                new MainDashboard(teacher);
            } else {
                tipLabel.setText("❌ 登录失败，账号或密码错误");
            }
        });
        setVisible(true);
    }

    private Teacher checkLogin(String user, String pass) {
        String sql = "SELECT * FROM teacher WHERE username=? AND password=?";
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setString(1, user);
            stmt.setString(2, pass);
            ResultSet rs = stmt.executeQuery();
            if(rs.next()){
                Teacher teacher = new Teacher();
                teacher.setUsername(rs.getString("username"));
                teacher.setPassword(rs.getString("password"));
                teacher.setTeacherId(rs.getLong("teacher_id"));
                teacher.setName(rs.getString("name"));
                teacher.setGender(rs.getString("gender"));
                teacher.setBirthDate(rs.getDate("birth_date"));
                teacher.setEthnicity(rs.getString("ethnicity"));
                teacher.setEducation(rs.getString("education"));
                teacher.setStartWorkDate(rs.getDate("start_work_date"));
                teacher.setTitle(rs.getString("title"));
                teacher.setBaseSalary(rs.getDouble("base_salary"));
                teacher.setPostSalary(rs.getDouble("post_salary"));
                teacher.setBonusSalary(rs.getDouble("bonus_salary"));
                teacher.setDeptId(rs.getLong("dept_id"));
                return teacher;
            }
        } catch (SQLException e) {
            JOptionPane.showMessageDialog(this, "数据库错误：" + e.getMessage());
        }
        return null;
    }
}</div>

    <p class="center"><strong>报告完成日期：</strong>2024年12月</p>
    <p class="center"><strong>总页数：</strong>约30页</p>
</div>

</body>
</html>
