<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>《数据库原理及应用》课程设计实验报告书</title>
    <style>
        body {
            font-family: "SimSun", "宋体", serif;
            line-height: 1.6;
            margin: 2cm;
            font-size: 12pt;
        }
        .cover {
            text-align: center;
            page-break-after: always;
        }
        .cover h1 {
            font-size: 18pt;
            font-weight: bold;
            margin: 2cm 0;
        }
        .cover h2 {
            font-size: 16pt;
            margin: 1cm 0;
        }
        .cover table {
            margin: 2cm auto;
            border-collapse: collapse;
            width: 60%;
        }
        .cover td {
            padding: 8px;
            border: 1px solid #000;
            text-align: left;
        }
        .abstract {
            page-break-before: always;
        }
        .toc {
            page-break-before: always;
        }
        .content {
            page-break-before: always;
        }
        h1 {
            font-size: 16pt;
            font-weight: bold;
            margin-top: 2em;
            margin-bottom: 1em;
        }
        h2 {
            font-size: 14pt;
            font-weight: bold;
            margin-top: 1.5em;
            margin-bottom: 0.8em;
        }
        h3 {
            font-size: 12pt;
            font-weight: bold;
            margin-top: 1em;
            margin-bottom: 0.5em;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 1em 0;
        }
        th, td {
            border: 1px solid #000;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f0f0f0;
            font-weight: bold;
        }
        .code {
            background-color: #f5f5f5;
            border: 1px solid #ddd;
            padding: 10px;
            font-family: "Courier New", monospace;
            font-size: 10pt;
            margin: 1em 0;
            white-space: pre-wrap;
        }
        .center {
            text-align: center;
        }
        ol, ul {
            margin: 0.5em 0;
            padding-left: 2em;
        }
        li {
            margin: 0.3em 0;
        }
        @media print {
            .page-break {
                page-break-before: always;
            }
        }
    </style>
</head>
<body>

<!-- 封面 -->
<div class="cover">
    <h1>《数据库原理及应用》课程设计实验报告书</h1>
    
    <h2>安徽工业大学计算机科学与技术学院</h2>
    
    <h2>题目：科研管理系统设计与实现</h2>
    
    <table>
        <tr>
            <td><strong>学号</strong></td>
            <td>[请填写学号]</td>
        </tr>
        <tr>
            <td><strong>姓名</strong></td>
            <td>[请填写姓名]</td>
        </tr>
        <tr>
            <td><strong>专业</strong></td>
            <td>计算机科学与技术</td>
        </tr>
        <tr>
            <td><strong>班级</strong></td>
            <td>[请填写班级]</td>
        </tr>
        <tr>
            <td><strong>指导教师</strong></td>
            <td>[请填写指导教师]</td>
        </tr>
        <tr>
            <td><strong>分数</strong></td>
            <td></td>
        </tr>
    </table>
    
    <p><strong>2024年12月</strong></p>
</div>

<!-- 摘要 -->
<div class="abstract">
    <h1 class="center">摘要</h1>
    
    <p>本课程设计选择科研管理系统作为开发目标，旨在通过实际项目开发加深对数据库原理及应用的理解。随着高等院校科研活动的日益增多，传统的人工管理方式已无法满足现代科研管理的需求，迫切需要一个高效、准确的信息化管理系统。</p>
    
    <p>本系统主要功能包括教师信息管理、科研项目管理、科研成果管理、成果排名管理、项目参与管理以及多维度的查询统计功能。系统能够实现教师基本信息的录入与维护、科研项目的全生命周期管理、科研成果的分类管理和排名统计，以及基于部门和教师的多角度数据查询分析。</p>
    
    <p>系统采用Java语言开发，使用MySQL数据库存储数据，基于Swing技术构建图形用户界面。经过测试，系统功能完整，性能稳定，用户界面友好，达到了预期的设计目标，能够有效提升科研管理的效率和准确性。</p>
    
    <p><strong>关键词：</strong>科研管理；数据库设计；Java；MySQL；Swing界面</p>
</div>

<!-- 目录 -->
<div class="toc">
    <h1 class="center">目录</h1>
    
    <p>1 系统需求分析 ......................................................... 2</p>
    <p>&nbsp;&nbsp;1.1 系统开发背景 .................................................. 2</p>
    <p>&nbsp;&nbsp;1.2 系统目标 ...................................................... 2</p>
    <p>&nbsp;&nbsp;1.3 功能需求 ...................................................... 2</p>
    <p>&nbsp;&nbsp;1.4 非功能需求 .................................................... 3</p>
    <p>&nbsp;&nbsp;1.5 系统约束 ...................................................... 3</p>
    <p>2 数据库的设计与实现 ................................................. 4</p>
    <p>&nbsp;&nbsp;2.1 数据库E-R图 ................................................... 4</p>
    <p>&nbsp;&nbsp;2.2 表结构设计 .................................................... 4</p>
    <p>&nbsp;&nbsp;2.3 数据库物理结构设计 ............................................ 5</p>
    <p>&nbsp;&nbsp;2.4 数据库索引设计 ................................................ 5</p>
    <p>&nbsp;&nbsp;2.5 数据库存储过程设计 ............................................ 5</p>
    <p>&nbsp;&nbsp;2.6 数据库触发器设计 .............................................. 5</p>
    <p>3 系统主要功能设计 ................................................... 6</p>
    <p>4 系统主要功能的实现及测试 ........................................... 7</p>
    <p>5 系统说明 ........................................................... 8</p>
    <p>&nbsp;&nbsp;5.1 系统开发环境 .................................................. 8</p>
    <p>&nbsp;&nbsp;5.2 系统安装、配置与发布应用程序的步骤 ........................... 8</p>
    <p>总结 ................................................................. 9</p>
    <p>附录：部分源代码 ..................................................... 10</p>
</div>

<!-- 正文内容 -->
<div class="content">
    <h1>1 系统需求分析</h1>
    
    <h2>1.1 系统开发背景</h2>
    <p>随着高等教育事业的快速发展和国家对科技创新的高度重视，高校作为科研创新的重要基地，其科研活动日益频繁，科研项目数量呈指数级增长，科研成果类型日趋多样化。据统计，近年来高校承担的国家级科研项目数量年均增长超过15%，省部级项目增长更是达到20%以上。在这种背景下，传统的纸质档案管理和人工统计方式已经完全无法满足现代科研管理的需求。</p>

    <p><strong>当前科研管理面临的主要问题：</strong></p>
    <ol>
        <li><strong>信息管理效率低下：</strong>
            <ul>
                <li>教师基本信息、项目申报信息、成果产出信息分散在不同部门管理</li>
                <li>信息检索困难，经常需要人工翻阅大量纸质档案</li>
                <li>统计工作量巨大，每次评估都需要大量人力物力</li>
                <li>信息更新不及时，存在数据滞后现象</li>
            </ul>
        </li>
        <li><strong>数据准确性难以保证：</strong>
            <ul>
                <li>人工录入过程中容易出现录入错误、重复录入等问题</li>
                <li>不同部门对同一数据的理解和记录方式存在差异</li>
                <li>缺乏有效的数据校验机制，错误数据难以及时发现</li>
                <li>历史数据的一致性维护困难</li>
            </ul>
        </li>
        <li><strong>信息共享困难：</strong>
            <ul>
                <li>各部门之间信息孤立，形成"信息孤岛"</li>
                <li>缺乏统一的数据标准和接口规范</li>
                <li>跨部门协作时信息传递效率低下</li>
                <li>重复建设现象严重，资源浪费</li>
            </ul>
        </li>
        <li><strong>决策支持不足：</strong>
            <ul>
                <li>缺乏实时的数据分析和统计报表</li>
                <li>无法快速响应上级部门的数据需求</li>
                <li>难以进行科研趋势分析和预测</li>
                <li>绩效评估缺乏客观的数据支撑</li>
            </ul>
        </li>
        <li><strong>管理流程不规范：</strong>
            <ul>
                <li>缺乏标准化的管理流程和操作规范</li>
                <li>审批流程冗长，效率低下</li>
                <li>权限管理不清晰，存在安全隐患</li>
                <li>缺乏有效的监督和控制机制</li>
            </ul>
        </li>
    </ol>

    <p><strong>信息化建设的必要性：</strong></p>
    <p>面对上述问题，构建一个集成化、智能化的科研管理系统已成为高校信息化建设的迫切需求。通过信息化手段，可以实现科研信息的统一管理、实时更新、快速检索和深度分析，从而大幅提升科研管理的效率和质量。同时，信息化系统还能够为科研决策提供数据支撑，促进科研资源的优化配置，推动科研创新能力的持续提升。</p>

    <p>因此，开发一个功能完善、技术先进、易于使用的科研管理系统，实现科研信息的数字化管理，提高管理效率和决策水平，不仅具有重要的现实意义，更是高校适应新时代发展要求的必然选择。</p>
    
    <h2>1.2 系统目标</h2>
    <p>本科研管理系统的设计与实现旨在构建一个集成化、智能化、标准化的科研信息管理平台，全面提升高校科研管理的现代化水平。系统将遵循"统一规划、分步实施、重点突破、全面推进"的建设原则，实现科研管理的数字化转型。</p>

    <p><strong>总体目标：</strong></p>
    <p>设计并实现一个高效、易用、稳定且可扩展的科研管理系统，满足高校科研管理的全方位需求，为科研创新提供强有力的信息化支撑。</p>

    <p><strong>具体目标：</strong></p>
    <ol>
        <li><strong>提高管理效率：</strong>
            <ul>
                <li>通过流程自动化，将传统的人工操作转变为系统自动处理</li>
                <li>实现科研信息的一站式管理，减少重复录入和多头管理</li>
                <li>建立快速响应机制，提高信息处理和反馈速度</li>
                <li>优化业务流程，消除管理环节中的冗余和瓶颈</li>
                <li>预期效率提升：日常管理效率提高60%以上，统计报表生成时间缩短80%</li>
            </ul>
        </li>
        <li><strong>保证数据准确性：</strong>
            <ul>
                <li>建立完整的数据约束和校验机制，从源头控制数据质量</li>
                <li>实现数据的统一标准化管理，确保数据格式和内容的一致性</li>
                <li>建立数据审核和确认流程，多层次保障数据准确性</li>
                <li>提供数据追溯功能，确保数据变更的可追踪性</li>
                <li>目标指标：数据准确率达到99.5%以上，数据一致性达到100%</li>
            </ul>
        </li>
        <li><strong>增强查询统计能力：</strong>
            <ul>
                <li>提供多维度、多层次的数据查询和分析功能</li>
                <li>支持复杂条件的组合查询和模糊查询</li>
                <li>实现实时统计和历史趋势分析</li>
                <li>提供可视化的数据展示和报表生成功能</li>
                <li>支持数据导出和打印，满足不同场景的使用需求</li>
            </ul>
        </li>
        <li><strong>改善用户体验：</strong>
            <ul>
                <li>设计直观友好的用户界面，符合用户操作习惯</li>
                <li>提供完善的帮助文档和操作指南</li>
                <li>实现响应式设计，适配不同设备和屏幕尺寸</li>
                <li>建立用户反馈机制，持续优化用户体验</li>
                <li>降低系统使用门槛，新用户培训时间控制在2小时以内</li>
            </ul>
        </li>
        <li><strong>确保系统安全性：</strong>
            <ul>
                <li>建立多层次的安全防护体系，保障数据和系统安全</li>
                <li>实现细粒度的权限控制，确保数据访问的合规性</li>
                <li>提供完整的操作日志记录，支持安全审计</li>
                <li>建立数据备份和恢复机制，确保数据安全</li>
            </ul>
        </li>
        <li><strong>支持系统扩展性：</strong>
            <ul>
                <li>采用模块化设计，支持功能的灵活扩展和定制</li>
                <li>预留标准接口，支持与其他系统的集成</li>
                <li>支持大数据量处理，满足未来发展需求</li>
                <li>提供配置管理功能，支持系统参数的灵活调整</li>
            </ul>
        </li>
    </ol>

    <p><strong>预期效果：</strong></p>
    <ul>
        <li>建立统一的科研信息管理平台，实现数据集中管理和共享</li>
        <li>提升科研管理的规范化和标准化水平</li>
        <li>为科研决策提供及时、准确的数据支撑</li>
        <li>促进科研资源的优化配置和高效利用</li>
        <li>推动科研管理模式的创新和发展</li>
    </ul>

    <h2>1.3 功能需求</h2>

    <h3>用户管理</h3>
    <p>用户管理是系统安全和权限控制的基础，确保只有合法用户才能访问系统，并根据用户角色分配相应的操作权限。</p>
    <ol>
        <li><strong>用户身份认证：</strong>
            <ul>
                <li>支持用户名密码登录方式</li>
                <li>实现登录失败次数限制，防止暴力破解</li>
                <li>提供密码强度检查和定期更换提醒</li>
                <li>支持用户登录状态保持和自动退出机制</li>
                <li>记录用户登录日志，包括登录时间、IP地址等信息</li>
            </ul>
        </li>
        <li><strong>角色权限管理：</strong>
            <ul>
                <li>系统管理员：拥有所有功能的访问权限，负责系统维护和用户管理</li>
                <li>科研管理员：负责科研项目和成果的审核管理，拥有数据修改权限</li>
                <li>普通教师：可以查看和管理自己的相关信息，提交项目申请和成果申报</li>
                <li>访客用户：只能查看公开的统计信息，无法访问详细数据</li>
            </ul>
        </li>
        <li><strong>用户信息管理：</strong>
            <ul>
                <li>支持用户基本信息的维护和更新</li>
                <li>提供用户头像上传和个人资料管理功能</li>
                <li>支持用户密码修改和安全问题设置</li>
                <li>实现用户状态管理（激活、禁用、锁定等）</li>
            </ul>
        </li>
    </ol>

    <h3>教师信息管理</h3>
    <p>教师信息管理是科研管理系统的核心模块之一，负责维护教师的基本信息、学术背景、科研能力等关键数据，为项目分配和成果统计提供基础支撑。</p>
    <ol>
        <li><strong>教师基本信息管理：</strong>
            <ul>
                <li>个人基本信息：姓名、性别、出生日期、民族、身份证号、联系方式等</li>
                <li>工作信息：工号、入职时间、所属部门、办公地点、邮箱地址等</li>
                <li>学历背景：最高学历、毕业院校、专业方向、学位获得时间等</li>
                <li>职业发展：当前职称、职称获得时间、职业发展轨迹记录等</li>
                <li>薪资信息：基本工资、岗位工资、绩效奖金、津贴补助等（加密存储）</li>
            </ul>
        </li>
        <li><strong>学术信息管理：</strong>
            <ul>
                <li>研究方向：主要研究领域、专业特长、学术兴趣等</li>
                <li>学术兼职：担任的学术职务、社会兼职、评审专家资格等</li>
                <li>学术荣誉：获得的学术奖励、荣誉称号、特殊津贴等</li>
                <li>国际交流：海外学习经历、国际合作项目、学术访问记录等</li>
            </ul>
        </li>
        <li><strong>教师信息维护：</strong>
            <ul>
                <li>新增教师：支持单个添加和批量导入两种方式</li>
                <li>信息修改：提供在线编辑功能，支持字段级别的权限控制</li>
                <li>信息删除：软删除机制，保留历史记录，支持数据恢复</li>
                <li>信息审核：重要信息变更需要经过审核流程</li>
                <li>版本控制：记录信息变更历史，支持版本对比和回滚</li>
            </ul>
        </li>
        <li><strong>高级查询功能：</strong>
            <ul>
                <li>基础查询：按姓名、工号、部门等基本条件查询</li>
                <li>组合查询：支持多条件组合查询，如职称+部门+研究方向</li>
                <li>模糊查询：支持关键词模糊匹配和智能搜索</li>
                <li>高级筛选：按年龄段、入职时间、学历层次等条件筛选</li>
                <li>自定义查询：用户可以保存常用查询条件，快速调用</li>
            </ul>
        </li>
        <li><strong>统计分析功能：</strong>
            <ul>
                <li>人员结构分析：按部门、职称、学历等维度统计人员分布</li>
                <li>年龄结构分析：教师队伍的年龄分布和发展趋势</li>
                <li>学历结构分析：不同学历层次教师的比例和变化</li>
                <li>职称结构分析：各职称级别的人员分布和晋升情况</li>
                <li>流动性分析：教师入职、离职、调动等流动情况统计</li>
            </ul>
        </li>
        <li><strong>数据导入导出：</strong>
            <ul>
                <li>支持Excel格式的批量数据导入，提供标准模板</li>
                <li>支持多种格式的数据导出（Excel、PDF、CSV等）</li>
                <li>提供数据校验功能，确保导入数据的准确性</li>
                <li>支持增量更新和全量替换两种导入模式</li>
            </ul>
        </li>
    </ol>

    <h3>科研项目管理</h3>
    <p>科研项目管理是系统的重要组成部分，涵盖项目的全生命周期管理，从项目申报、立项、执行到结题验收的全过程跟踪管理。</p>
    <ol>
        <li><strong>项目基本信息管理：</strong>
            <ul>
                <li>项目标识：项目编号、项目名称、项目类型、项目级别等</li>
                <li>项目分类：按学科分类、按资助机构分类、按项目性质分类</li>
                <li>时间管理：申报时间、立项时间、开始时间、计划结束时间、实际结束时间</li>
                <li>项目描述：项目摘要、研究内容、技术路线、预期目标等</li>
                <li>关键词标签：便于项目检索和分类统计</li>
            </ul>
        </li>
        <li><strong>项目团队管理：</strong>
            <ul>
                <li>项目负责人：主持人信息、联系方式、学术背景等</li>
                <li>项目参与人员：参与教师列表、角色分工、职责描述</li>
                <li>外部合作：合作单位信息、合作方式、合作内容</li>
                <li>学生参与：参与的研究生、本科生信息和培养情况</li>
                <li>团队变更：人员调整记录、变更原因、审批流程</li>
            </ul>
        </li>
        <li><strong>项目经费管理：</strong>
            <ul>
                <li>经费预算：总预算、分年度预算、科目预算分配</li>
                <li>经费来源：资助机构、资助金额、配套资金等</li>
                <li>经费使用：实际支出记录、支出科目、支出凭证</li>
                <li>经费监控：预算执行进度、超支预警、结余处理</li>
                <li>财务报表：经费使用报表、审计报告、决算报告</li>
            </ul>
        </li>
        <li><strong>项目进度管理：</strong>
            <ul>
                <li>里程碑管理：关键节点设置、完成情况跟踪</li>
                <li>进度报告：定期进度汇报、阶段性总结</li>
                <li>风险管理：风险识别、风险评估、应对措施</li>
                <li>变更管理：项目变更申请、审批、实施跟踪</li>
                <li>质量控制：质量标准、检查记录、改进措施</li>
            </ul>
        </li>
        <li><strong>项目文档管理：</strong>
            <ul>
                <li>申报材料：项目申请书、可行性报告、预算书等</li>
                <li>过程文档：会议记录、工作报告、技术文档等</li>
                <li>成果文档：研究报告、技术报告、专利申请等</li>
                <li>管理文档：合同协议、变更通知、审批文件等</li>
                <li>版本控制：文档版本管理、修改记录、权限控制</li>
            </ul>
        </li>
        <li><strong>项目评估与考核：</strong>
            <ul>
                <li>中期评估：项目执行情况评估、问题识别、改进建议</li>
                <li>结题验收：成果验收、经费审计、绩效评价</li>
                <li>后评估：项目效果跟踪、社会效益评估</li>
                <li>绩效指标：论文发表、专利申请、人才培养等量化指标</li>
                <li>评估报告：综合评估报告、改进建议、经验总结</li>
            </ul>
        </li>
    </ol>

    <h3>科研成果管理</h3>
    <p>科研成果管理是衡量科研活动效果的重要环节，系统需要全面记录和管理各类科研成果，为科研评价和绩效考核提供数据支撑。</p>
    <ol>
        <li><strong>成果分类管理：</strong>
            <ul>
                <li>学术论文：期刊论文、会议论文、综述文章等</li>
                <li>知识产权：发明专利、实用新型、外观设计、软件著作权等</li>
                <li>科技奖励：国家级奖励、省部级奖励、行业奖励等</li>
                <li>著作教材：学术专著、编著、教材、译著等</li>
                <li>标准规范：国家标准、行业标准、企业标准等</li>
                <li>技术转化：技术转让、成果转化、产业化应用等</li>
                <li>咨询报告：政策建议、咨询报告、调研报告等</li>
            </ul>
        </li>
        <li><strong>成果详细信息管理：</strong>
            <ul>
                <li>基本信息：成果名称、成果类型、完成时间、发表/授权时间</li>
                <li>质量等级：影响因子、期刊级别、会议等级、奖励等级</li>
                <li>发表信息：期刊名称、卷期号、页码、DOI、检索情况</li>
                <li>专利信息：专利号、申请日期、授权日期、专利状态</li>
                <li>获奖信息：奖励名称、奖励等级、颁奖机构、获奖时间</li>
                <li>转化信息：转化方式、转化金额、合作企业、应用效果</li>
            </ul>
        </li>
        <li><strong>成果贡献度管理：</strong>
            <ul>
                <li>作者排名：第一作者、通讯作者、共同作者等角色标识</li>
                <li>贡献比例：各参与人员的贡献度分配和计算</li>
                <li>署名单位：第一署名单位、合作单位、通讯单位</li>
                <li>资助信息：资助项目、资助机构、资助编号</li>
                <li>合作情况：校内合作、校外合作、国际合作标识</li>
            </ul>
        </li>
        <li><strong>成果质量评价：</strong>
            <ul>
                <li>影响力评价：被引次数、下载次数、社会影响等</li>
                <li>同行评议：专家评价、同行认可度、学术声誉</li>
                <li>应用价值：实际应用情况、经济效益、社会效益</li>
                <li>创新程度：原创性评价、技术先进性、理论贡献</li>
                <li>质量等级：根据多维度指标综合评定质量等级</li>
            </ul>
        </li>
        <li><strong>成果统计分析：</strong>
            <ul>
                <li>数量统计：按类型、级别、时间等维度统计成果数量</li>
                <li>质量分析：高水平成果比例、影响因子分布等</li>
                <li>趋势分析：成果产出趋势、质量变化趋势</li>
                <li>对比分析：部门间对比、个人间对比、年度对比</li>
                <li>绩效评估：基于成果的绩效评价和排名</li>
            </ul>
        </li>
        <li><strong>成果展示与推广：</strong>
            <ul>
                <li>成果展示：成果库展示、优秀成果推荐</li>
                <li>成果推广：技术推广、成果宣传、媒体报道</li>
                <li>成果交流：学术交流、技术交流、合作洽谈</li>
                <li>成果保护：知识产权保护、商业秘密保护</li>
                <li>成果服务：技术咨询、技术服务、技术培训</li>
            </ul>
        </li>
    </ol>

    <h3>查询统计功能</h3>
    <p>查询统计功能是系统的核心应用功能，为用户提供灵活、高效的数据查询和统计分析能力，支持科研管理决策。</p>
    <ol>
        <li><strong>多维度查询功能：</strong>
            <ul>
                <li>教师维度：按教师查询其参与的项目、获得的成果、承担的任务</li>
                <li>项目维度：按项目查询参与人员、经费使用、产出成果</li>
                <li>成果维度：按成果查询相关项目、参与人员、影响情况</li>
                <li>部门维度：按部门统计人员结构、项目情况、成果产出</li>
                <li>时间维度：按年度、季度、月度等时间段进行统计分析</li>
            </ul>
        </li>
        <li><strong>高级查询功能：</strong>
            <ul>
                <li>组合查询：支持多个条件的逻辑组合（AND、OR、NOT）</li>
                <li>模糊查询：支持关键词模糊匹配和全文检索</li>
                <li>范围查询：支持数值范围、时间范围、等级范围查询</li>
                <li>排序功能：支持多字段排序和自定义排序规则</li>
                <li>分页显示：大数据量查询结果的分页展示和快速定位</li>
            </ul>
        </li>
        <li><strong>统计分析功能：</strong>
            <ul>
                <li>描述性统计：计数、求和、平均值、最大值、最小值等</li>
                <li>分组统计：按不同维度分组统计，支持多级分组</li>
                <li>趋势分析：时间序列分析、增长率计算、趋势预测</li>
                <li>对比分析：同比、环比、横向对比、纵向对比</li>
                <li>相关性分析：变量间相关性分析、影响因素分析</li>
            </ul>
        </li>
        <li><strong>可视化展示：</strong>
            <ul>
                <li>图表展示：柱状图、折线图、饼图、散点图等</li>
                <li>仪表盘：关键指标仪表盘、实时数据监控</li>
                <li>地图展示：地理分布图、热力图、区域统计</li>
                <li>交互式图表：支持钻取、筛选、缩放等交互操作</li>
                <li>自定义图表：用户可自定义图表类型和样式</li>
            </ul>
        </li>
        <li><strong>报表生成功能：</strong>
            <ul>
                <li>标准报表：预定义的常用统计报表模板</li>
                <li>自定义报表：用户可自定义报表格式和内容</li>
                <li>定时报表：定时生成和发送报表</li>
                <li>多格式导出：支持Excel、PDF、Word、CSV等格式</li>
                <li>报表分发：邮件发送、系统通知、文件共享</li>
            </ul>
        </li>
        <li><strong>数据挖掘功能：</strong>
            <ul>
                <li>模式识别：发现数据中的规律和模式</li>
                <li>异常检测：识别异常数据和异常行为</li>
                <li>预测分析：基于历史数据进行趋势预测</li>
                <li>聚类分析：数据聚类和分类分析</li>
                <li>关联规则：发现数据间的关联关系</li>
            </ul>
        </li>
    </ol>

    <h3>系统管理</h3>
    <p>系统管理功能确保系统的安全、稳定、高效运行，为系统维护和管理提供全面的工具和手段。</p>
    <ol>
        <li><strong>用户权限管理：</strong>
            <ul>
                <li>角色定义：系统管理员、科研管理员、普通教师、访客等角色</li>
                <li>权限分配：功能权限、数据权限、操作权限的细粒度控制</li>
                <li>权限继承：角色权限继承和权限组合机制</li>
                <li>动态授权：临时权限授予和权限委托机制</li>
                <li>权限审计：权限使用记录和权限变更审计</li>
            </ul>
        </li>
        <li><strong>系统配置管理：</strong>
            <ul>
                <li>参数配置：系统运行参数、业务规则参数配置</li>
                <li>界面配置：用户界面布局、主题样式配置</li>
                <li>流程配置：业务流程定义和流程参数配置</li>
                <li>集成配置：外部系统接口配置和集成参数</li>
                <li>性能配置：系统性能参数和优化配置</li>
            </ul>
        </li>
        <li><strong>数据管理：</strong>
            <ul>
                <li>数据备份：定时备份、增量备份、全量备份策略</li>
                <li>数据恢复：数据恢复、回滚操作、灾难恢复</li>
                <li>数据清理：过期数据清理、垃圾数据清理</li>
                <li>数据迁移：数据导入导出、系统迁移、数据同步</li>
                <li>数据质量：数据校验、数据修复、质量监控</li>
            </ul>
        </li>
        <li><strong>系统监控：</strong>
            <ul>
                <li>性能监控：系统性能指标监控、资源使用监控</li>
                <li>运行监控：系统运行状态、服务可用性监控</li>
                <li>安全监控：安全事件监控、异常行为检测</li>
                <li>用户监控：用户行为监控、访问统计分析</li>
                <li>告警机制：异常告警、阈值告警、自动通知</li>
            </ul>
        </li>
        <li><strong>日志管理：</strong>
            <ul>
                <li>操作日志：用户操作记录、系统操作记录</li>
                <li>访问日志：用户访问记录、API调用记录</li>
                <li>错误日志：系统错误记录、异常信息记录</li>
                <li>安全日志：安全事件记录、权限变更记录</li>
                <li>日志分析：日志统计分析、行为模式分析</li>
            </ul>
        </li>
        <li><strong>系统维护：</strong>
            <ul>
                <li>版本管理：系统版本控制、升级管理</li>
                <li>补丁管理：安全补丁、功能补丁的安装管理</li>
                <li>维护计划：定期维护计划、维护窗口管理</li>
                <li>故障处理：故障诊断、故障修复、应急响应</li>
                <li>文档管理：系统文档、操作手册、维护记录</li>
            </ul>
        </li>
    </ol>

    <h2>1.4 非功能需求</h2>

    <h3>性能需求</h3>
    <ol>
        <li><strong>响应时间：</strong>系统各项操作响应时间不超过3秒。</li>
        <li><strong>并发处理：</strong>支持多用户同时操作，保证系统稳定性。</li>
        <li><strong>数据处理能力：</strong>支持大量数据的存储和快速检索。</li>
    </ol>

    <h3>安全性需求</h3>
    <ol>
        <li><strong>数据安全：</strong>采用合适的数据存储和备份策略，确保数据安全。</li>
        <li><strong>访问控制：</strong>实施用户身份验证，防止非法访问。</li>
        <li><strong>数据完整性：</strong>通过数据库约束确保数据的完整性和一致性。</li>
    </ol>

    <h3>可用性需求</h3>
    <ol>
        <li><strong>界面友好：</strong>提供直观、易用的图形用户界面。</li>
        <li><strong>操作简便：</strong>操作流程简单明了，降低学习成本。</li>
        <li><strong>系统稳定性：</strong>保证99%以上的运行时间。</li>
    </ol>

    <h3>兼容性需求</h3>
    <ol>
        <li><strong>平台兼容：</strong>支持Windows操作系统。</li>
        <li><strong>数据库兼容：</strong>兼容MySQL数据库系统。</li>
        <li><strong>版本兼容：</strong>支持Java 8及以上版本。</li>
    </ol>

    <h2>1.5 系统约束</h2>

    <h3>技术约束</h3>
    <ol>
        <li><strong>开发语言：</strong>使用Java语言进行开发。</li>
        <li><strong>数据库系统：</strong>使用MySQL数据库管理系统。</li>
        <li><strong>用户界面：</strong>基于Java Swing技术构建图形用户界面。</li>
        <li><strong>开发环境：</strong>使用IntelliJ IDEA集成开发环境。</li>
    </ol>

    <h3>硬件约束</h3>
    <ol>
        <li><strong>服务器配置：</strong>需要安装MySQL数据库服务器。</li>
        <li><strong>客户端配置：</strong>需要安装Java运行环境（JRE 8+）。</li>
        <li><strong>存储要求：</strong>至少500MB可用磁盘空间。</li>
    </ol>

    <h3>法律约束</h3>
    <ol>
        <li><strong>数据保护：</strong>遵守相关数据保护法规，保护用户隐私。</li>
        <li><strong>知识产权：</strong>确保软件知识产权的合法使用。</li>
    </ol>

    <div class="page-break"></div>
    <h1>2 数据库的设计与实现</h1>

    <h2>2.1 数据库E-R图</h2>
    <p>数据库设计是系统的核心基础，通过科学合理的实体关系设计，确保数据的完整性、一致性和高效性。本系统采用关系型数据库设计方法，遵循数据库设计的三大范式，避免数据冗余和异常。</p>

    <h3>2.1.1 概念模型设计</h3>
    <p>系统的概念模型包含以下主要实体和关系：</p>

    <div class="center">
        <p><strong>科研管理系统概念E-R图</strong></p>
        <div style="border: 1px solid #000; padding: 20px; margin: 20px 0; font-family: monospace; background-color: #f9f9f9;">
            <pre>
                    ┌─────────┐         ┌─────────┐         ┌─────────┐
                    │  部门   │         │  教师   │         │  用户   │
                    │Department│    1:N  │ Teacher │    1:1  │  User   │
                    └─────────┘ ────────└─────────┘ ────────└─────────┘
                         │                   │                   │
                         │                   │                   │
                         │              ┌─────────┐              │
                         │         M:N  │项目参与 │         1:N  │
                         │ ─────────────│Participation│──────────│
                         │              └─────────┘              │
                         │                   │                   │
                    ┌─────────┐         ┌─────────┐         ┌─────────┐
                    │  项目   │    1:N  │  成果   │    M:N  │成果排名 │
                    │ Project │ ────────│Achievement│────────│ Ranking │
                    └─────────┘         └─────────┘         └─────────┘
                         │                   │                   │
                         │                   │                   │
                    ┌─────────┐         ┌─────────┐         ┌─────────┐
                    │项目经费 │         │成果类型 │         │  评价   │
                    │  Fund   │         │Category │         │Evaluation│
                    └─────────┘         └─────────┘         └─────────┘
            </pre>
        </div>
    </div>

    <h3>2.1.2 主要实体详细描述</h3>
    <ol>
        <li><strong>教师（Teacher）实体：</strong>
            <ul>
                <li>属性：教师ID、姓名、性别、出生日期、民族、学历、职称、薪资信息、部门ID等</li>
                <li>主键：teacher_id（教师唯一标识符）</li>
                <li>外键：dept_id（关联部门表）</li>
                <li>约束：用户名唯一性约束、必填字段非空约束</li>
            </ul>
        </li>
        <li><strong>项目（Project）实体：</strong>
            <ul>
                <li>属性：项目ID、项目名称、项目类型、负责人ID、经费、起止时间等</li>
                <li>主键：project_id（项目唯一标识符）</li>
                <li>外键：manager_id（关联教师表，项目负责人）</li>
                <li>约束：项目名称非空、起止时间逻辑约束</li>
            </ul>
        </li>
        <li><strong>成果（Achievement）实体：</strong>
            <ul>
                <li>属性：成果ID、项目ID、成果类别、级别、等级、批准日期等</li>
                <li>主键：achievement_id（成果唯一标识符）</li>
                <li>外键：project_id（关联项目表）</li>
                <li>约束：成果类别非空、等级范围约束</li>
            </ul>
        </li>
        <li><strong>项目参与（ProjectParticipation）关系实体：</strong>
            <ul>
                <li>属性：教师ID、项目ID、参与角色、参与时间、贡献度等</li>
                <li>主键：(teacher_id, project_id)（复合主键）</li>
                <li>外键：teacher_id（关联教师表）、project_id（关联项目表）</li>
                <li>约束：贡献度范围约束、角色枚举约束</li>
            </ul>
        </li>
        <li><strong>成果排名（AchievementRanking）关系实体：</strong>
            <ul>
                <li>属性：成果ID、教师ID、排名、贡献度、署名顺序等</li>
                <li>主键：(achievement_id, teacher_id)（复合主键）</li>
                <li>外键：achievement_id（关联成果表）、teacher_id（关联教师表）</li>
                <li>约束：排名唯一性约束、贡献度总和约束</li>
            </ul>
        </li>
    </ol>

    <h3>2.1.3 实体关系详细说明</h3>
    <ol>
        <li><strong>教师与部门关系（N:1）：</strong>
            <ul>
                <li>一个部门可以有多名教师，一名教师只能属于一个部门</li>
                <li>通过dept_id外键建立关联关系</li>
                <li>支持教师部门调动的历史记录</li>
            </ul>
        </li>
        <li><strong>教师与项目关系（M:N）：</strong>
            <ul>
                <li>一名教师可以参与多个项目，一个项目可以有多名教师参与</li>
                <li>通过ProjectParticipation中间表建立多对多关系</li>
                <li>记录教师在项目中的具体角色和贡献度</li>
            </ul>
        </li>
        <li><strong>项目与成果关系（1:N）：</strong>
            <ul>
                <li>一个项目可以产生多个成果，一个成果只能属于一个项目</li>
                <li>通过project_id外键建立关联关系</li>
                <li>支持成果与项目的强关联约束</li>
            </ul>
        </li>
        <li><strong>教师与成果关系（M:N）：</strong>
            <ul>
                <li>一名教师可以参与多个成果，一个成果可以有多名教师参与</li>
                <li>通过AchievementRanking中间表建立多对多关系</li>
                <li>记录教师在成果中的排名和贡献情况</li>
            </ul>
        </li>
    </ol>

    <h3>2.1.4 数据完整性约束</h3>
    <ol>
        <li><strong>实体完整性：</strong>每个实体都有唯一的主键标识</li>
        <li><strong>参照完整性：</strong>外键约束确保关联数据的一致性</li>
        <li><strong>用户定义完整性：</strong>业务规则约束，如日期逻辑、数值范围等</li>
        <li><strong>域完整性：</strong>字段类型、长度、格式等约束</li>
    </ol>

    <h2>2.2 表结构设计</h2>

    <h3>Teacher表（教师信息表）</h3>
    <table>
        <tr>
            <th>字段名</th>
            <th>数据类型</th>
            <th>约束</th>
            <th>备注</th>
        </tr>
        <tr>
            <td>teacher_id</td>
            <td>BIGINT</td>
            <td>PRIMARY KEY, AUTO_INCREMENT</td>
            <td>教师唯一标识符</td>
        </tr>
        <tr>
            <td>username</td>
            <td>VARCHAR(50)</td>
            <td>NOT NULL, UNIQUE</td>
            <td>登录用户名</td>
        </tr>
        <tr>
            <td>password</td>
            <td>VARCHAR(100)</td>
            <td>NOT NULL</td>
            <td>登录密码</td>
        </tr>
        <tr>
            <td>name</td>
            <td>VARCHAR(100)</td>
            <td>NOT NULL</td>
            <td>教师姓名</td>
        </tr>
        <tr>
            <td>gender</td>
            <td>VARCHAR(10)</td>
            <td></td>
            <td>性别</td>
        </tr>
        <tr>
            <td>birth_date</td>
            <td>DATE</td>
            <td></td>
            <td>出生日期</td>
        </tr>
        <tr>
            <td>ethnicity</td>
            <td>VARCHAR(50)</td>
            <td></td>
            <td>民族</td>
        </tr>
        <tr>
            <td>education</td>
            <td>VARCHAR(100)</td>
            <td></td>
            <td>学历</td>
        </tr>
        <tr>
            <td>start_work_date</td>
            <td>DATE</td>
            <td></td>
            <td>参加工作日期</td>
        </tr>
        <tr>
            <td>title</td>
            <td>VARCHAR(100)</td>
            <td></td>
            <td>职称</td>
        </tr>
        <tr>
            <td>base_salary</td>
            <td>DECIMAL(10,2)</td>
            <td></td>
            <td>基本工资</td>
        </tr>
        <tr>
            <td>post_salary</td>
            <td>DECIMAL(10,2)</td>
            <td></td>
            <td>岗位工资</td>
        </tr>
        <tr>
            <td>bonus_salary</td>
            <td>DECIMAL(10,2)</td>
            <td></td>
            <td>奖金</td>
        </tr>
        <tr>
            <td>dept_id</td>
            <td>BIGINT</td>
            <td></td>
            <td>部门ID</td>
        </tr>
    </table>

    <h3>Project表（项目信息表）</h3>
    <table>
        <tr>
            <th>字段名</th>
            <th>数据类型</th>
            <th>约束</th>
            <th>备注</th>
        </tr>
        <tr>
            <td>project_id</td>
            <td>INT</td>
            <td>PRIMARY KEY, AUTO_INCREMENT</td>
            <td>项目唯一标识符</td>
        </tr>
        <tr>
            <td>project_name</td>
            <td>VARCHAR(200)</td>
            <td>NOT NULL</td>
            <td>项目名称</td>
        </tr>
        <tr>
            <td>manager_id</td>
            <td>BIGINT</td>
            <td>FOREIGN KEY</td>
            <td>项目负责人ID</td>
        </tr>
        <tr>
            <td>fund</td>
            <td>DECIMAL(15,2)</td>
            <td></td>
            <td>项目经费</td>
        </tr>
        <tr>
            <td>start_date</td>
            <td>DATE</td>
            <td></td>
            <td>项目开始日期</td>
        </tr>
        <tr>
            <td>end_date</td>
            <td>DATE</td>
            <td></td>
            <td>项目结束日期</td>
        </tr>
    </table>

    <h3>Achievement表（成果信息表）</h3>
    <table>
        <tr>
            <th>字段名</th>
            <th>数据类型</th>
            <th>约束</th>
            <th>备注</th>
        </tr>
        <tr>
            <td>achievement_id</td>
            <td>INT</td>
            <td>PRIMARY KEY, AUTO_INCREMENT</td>
            <td>成果唯一标识符</td>
        </tr>
        <tr>
            <td>project_id</td>
            <td>INT</td>
            <td>FOREIGN KEY</td>
            <td>关联项目ID</td>
        </tr>
        <tr>
            <td>category</td>
            <td>VARCHAR(100)</td>
            <td>NOT NULL</td>
            <td>成果类别</td>
        </tr>
        <tr>
            <td>level</td>
            <td>VARCHAR(50)</td>
            <td></td>
            <td>成果级别</td>
        </tr>
        <tr>
            <td>grade</td>
            <td>INT</td>
            <td></td>
            <td>成果等级</td>
        </tr>
        <tr>
            <td>approval_date</td>
            <td>DATE</td>
            <td></td>
            <td>批准日期</td>
        </tr>
    </table>

    <h3>ProjectParticipation表（项目参与表）</h3>
    <table>
        <tr>
            <th>字段名</th>
            <th>数据类型</th>
            <th>约束</th>
            <th>备注</th>
        </tr>
        <tr>
            <td>teacher_id</td>
            <td>BIGINT</td>
            <td>FOREIGN KEY</td>
            <td>教师ID</td>
        </tr>
        <tr>
            <td>project_id</td>
            <td>INT</td>
            <td>FOREIGN KEY</td>
            <td>项目ID</td>
        </tr>
        <tr>
            <td>role</td>
            <td>VARCHAR(100)</td>
            <td></td>
            <td>参与角色</td>
        </tr>
        <tr>
            <td colspan="4"><strong>PRIMARY KEY (teacher_id, project_id)</strong> - 复合主键</td>
        </tr>
    </table>

    <h3>AchievementRanking表（成果排名表）</h3>
    <table>
        <tr>
            <th>字段名</th>
            <th>数据类型</th>
            <th>约束</th>
            <th>备注</th>
        </tr>
        <tr>
            <td>achievement_id</td>
            <td>INT</td>
            <td>FOREIGN KEY</td>
            <td>成果ID</td>
        </tr>
        <tr>
            <td>teacher_id</td>
            <td>BIGINT</td>
            <td>FOREIGN KEY</td>
            <td>教师ID</td>
        </tr>
        <tr>
            <td>ranking</td>
            <td>INT</td>
            <td>NOT NULL</td>
            <td>排名</td>
        </tr>
        <tr>
            <td colspan="4"><strong>PRIMARY KEY (achievement_id, teacher_id)</strong> - 复合主键</td>
        </tr>
    </table>

    <h2>2.3 数据库物理结构设计</h2>

    <h3>（1）数据库物理结构图</h3>
    <p>系统使用MySQL数据库，数据库名称为"keshe"。各表之间的外键关系如下：</p>
    <ul>
        <li>ProjectParticipation.teacher_id → Teacher.teacher_id</li>
        <li>ProjectParticipation.project_id → Project.project_id</li>
        <li>Project.manager_id → Teacher.teacher_id</li>
        <li>Achievement.project_id → Project.project_id</li>
        <li>AchievementRanking.achievement_id → Achievement.achievement_id</li>
        <li>AchievementRanking.teacher_id → Teacher.teacher_id</li>
    </ul>

    <h3>（2）建库建表语句</h3>

    <div class="code">-- 创建数据库
CREATE DATABASE keshe CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE keshe;

-- 创建教师表
CREATE TABLE Teacher (
    teacher_id BIGINT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(100) NOT NULL,
    name VARCHAR(100) NOT NULL,
    gender VARCHAR(10),
    birth_date DATE,
    ethnicity VARCHAR(50),
    education VARCHAR(100),
    start_work_date DATE,
    title VARCHAR(100),
    base_salary DECIMAL(10,2),
    post_salary DECIMAL(10,2),
    bonus_salary DECIMAL(10,2),
    dept_id BIGINT
);

-- 创建项目表
CREATE TABLE Project (
    project_id INT AUTO_INCREMENT PRIMARY KEY,
    project_name VARCHAR(200) NOT NULL,
    manager_id BIGINT,
    fund DECIMAL(15,2),
    start_date DATE,
    end_date DATE,
    FOREIGN KEY (manager_id) REFERENCES Teacher(teacher_id)
);

-- 创建成果表
CREATE TABLE Achievement (
    achievement_id INT AUTO_INCREMENT PRIMARY KEY,
    project_id INT,
    category VARCHAR(100) NOT NULL,
    level VARCHAR(50),
    grade INT,
    approval_date DATE,
    FOREIGN KEY (project_id) REFERENCES Project(project_id)
);

-- 创建项目参与表
CREATE TABLE ProjectParticipation (
    teacher_id BIGINT,
    project_id INT,
    role VARCHAR(100),
    PRIMARY KEY (teacher_id, project_id),
    FOREIGN KEY (teacher_id) REFERENCES Teacher(teacher_id),
    FOREIGN KEY (project_id) REFERENCES Project(project_id)
);

-- 创建成果排名表
CREATE TABLE AchievementRanking (
    achievement_id INT,
    teacher_id BIGINT,
    ranking INT NOT NULL,
    PRIMARY KEY (achievement_id, teacher_id),
    FOREIGN KEY (achievement_id) REFERENCES Achievement(achievement_id),
    FOREIGN KEY (teacher_id) REFERENCES Teacher(teacher_id)
);</div>

    <h2>2.4 数据库索引设计</h2>
    <p>为提高查询性能，在以下字段上创建索引：</p>

    <div class="code">-- 在Teacher表上创建索引
CREATE INDEX idx_teacher_dept ON Teacher(dept_id);
CREATE INDEX idx_teacher_title ON Teacher(title);
CREATE INDEX idx_teacher_username ON Teacher(username);

-- 在Project表上创建索引
CREATE INDEX idx_project_manager ON Project(manager_id);
CREATE INDEX idx_project_name ON Project(project_name);

-- 在Achievement表上创建索引
CREATE INDEX idx_achievement_project ON Achievement(project_id);
CREATE INDEX idx_achievement_category ON Achievement(category);

-- 在AchievementRanking表上创建索引
CREATE INDEX idx_ranking_teacher ON AchievementRanking(teacher_id);
CREATE INDEX idx_ranking_achievement ON AchievementRanking(achievement_id);</div>

    <h2>2.5 数据库存储过程设计</h2>
    <p>本系统暂未使用存储过程，所有业务逻辑在应用层实现，保持系统的简洁性和可维护性。</p>

    <h2>2.6 数据库触发器设计</h2>
    <p>本系统暂未使用触发器，通过应用程序逻辑保证数据的完整性和一致性。</p>

    <div class="page-break"></div>
    <h1>3 系统主要功能设计</h1>

    <h2>3.1 登录功能模块设计</h2>

    <h3>3.1.1 设计思想</h3>
    <p>登录功能是系统安全的第一道防线，采用多层次的身份验证机制，确保只有合法用户才能访问系统。设计遵循安全性、易用性和可扩展性原则，为后续的权限控制奠定基础。</p>

    <h3>3.1.2 功能需求分析</h3>
    <ol>
        <li><strong>基本认证需求：</strong>
            <ul>
                <li>支持用户名密码登录方式</li>
                <li>密码加密存储和传输</li>
                <li>登录状态保持和会话管理</li>
                <li>自动登出和强制登出功能</li>
            </ul>
        </li>
        <li><strong>安全防护需求：</strong>
            <ul>
                <li>防止暴力破解攻击</li>
                <li>登录失败次数限制</li>
                <li>账户锁定和解锁机制</li>
                <li>异常登录检测和告警</li>
            </ul>
        </li>
        <li><strong>用户体验需求：</strong>
            <ul>
                <li>友好的登录界面设计</li>
                <li>清晰的错误提示信息</li>
                <li>记住用户名功能</li>
                <li>快速登录和单点登录</li>
            </ul>
        </li>
    </ol>

    <h3>3.1.3 详细设计流程</h3>
    <ol>
        <li><strong>用户输入阶段：</strong>
            <ul>
                <li>用户在登录界面输入用户名和密码</li>
                <li>前端进行基本格式验证（非空、长度等）</li>
                <li>密码字段采用掩码显示，保护用户隐私</li>
                <li>提供验证码输入框，防止自动化攻击</li>
            </ul>
        </li>
        <li><strong>数据传输阶段：</strong>
            <ul>
                <li>对用户密码进行客户端加密处理</li>
                <li>通过HTTPS协议安全传输登录数据</li>
                <li>添加时间戳和随机数防止重放攻击</li>
                <li>对请求数据进行完整性校验</li>
            </ul>
        </li>
        <li><strong>服务端验证阶段：</strong>
            <ul>
                <li>检查用户账户状态（是否存在、是否锁定）</li>
                <li>验证密码正确性（与数据库存储的加密密码比对）</li>
                <li>检查登录失败次数，判断是否需要锁定账户</li>
                <li>记录登录尝试日志，包括成功和失败记录</li>
            </ul>
        </li>
        <li><strong>会话建立阶段：</strong>
            <ul>
                <li>生成唯一的会话标识符（Session ID）</li>
                <li>设置会话超时时间和安全属性</li>
                <li>将用户信息和权限信息存储到会话中</li>
                <li>返回登录成功响应和会话令牌</li>
            </ul>
        </li>
        <li><strong>界面跳转阶段：</strong>
            <ul>
                <li>验证成功后跳转到系统主界面</li>
                <li>显示用户欢迎信息和基本状态</li>
                <li>加载用户个性化配置和权限菜单</li>
                <li>验证失败则显示具体错误信息</li>
            </ul>
        </li>
    </ol>

    <h3>3.1.4 时序图设计</h3>
    <div class="code">参与者: 用户, 登录界面, 验证服务, 数据库, 主界面

用户 -> 登录界面: 1. 输入用户名和密码
登录界面 -> 登录界面: 2. 前端数据验证
登录界面 -> 验证服务: 3. 发送登录请求（加密）
验证服务 -> 数据库: 4. 查询用户信息
数据库 -> 验证服务: 5. 返回用户数据
验证服务 -> 验证服务: 6. 密码验证和权限检查
验证服务 -> 数据库: 7. 记录登录日志
验证服务 -> 登录界面: 8. 返回验证结果
alt 登录成功
    登录界面 -> 主界面: 9a. 跳转到主界面
    主界面 -> 用户: 10a. 显示系统主页
else 登录失败
    登录界面 -> 用户: 9b. 显示错误信息
end</div>

    <h3>3.1.5 异常处理设计</h3>
    <ol>
        <li><strong>用户名不存在：</strong>提示"用户名或密码错误"（不透露具体错误原因）</li>
        <li><strong>密码错误：</strong>记录失败次数，超过限制则锁定账户</li>
        <li><strong>账户被锁定：</strong>提示账户锁定信息和解锁方式</li>
        <li><strong>网络异常：</strong>提示网络连接问题，建议重试</li>
        <li><strong>服务器异常：</strong>记录错误日志，提示系统维护信息</li>
    </ol>

    <h3>3.1.6 安全机制设计</h3>
    <ol>
        <li><strong>密码安全：</strong>
            <ul>
                <li>密码复杂度要求：至少8位，包含字母、数字、特殊字符</li>
                <li>密码加密存储：使用SHA-256+盐值进行加密</li>
                <li>密码定期更换：提醒用户定期更换密码</li>
            </ul>
        </li>
        <li><strong>会话安全：</strong>
            <ul>
                <li>会话超时：30分钟无操作自动登出</li>
                <li>单点登录：同一账户只允许一个活跃会话</li>
                <li>会话加密：会话数据采用AES加密存储</li>
            </ul>
        </li>
        <li><strong>访问控制：</strong>
            <ul>
                <li>IP白名单：限制特定IP地址访问</li>
                <li>时间限制：限制登录时间段</li>
                <li>设备绑定：绑定特定设备或浏览器</li>
            </ul>
        </li>
    </ol>

    <h2>3.2 教师信息管理功能模块设计</h2>
    <p><strong>设计思想：</strong>提供完整的教师信息CRUD操作，支持按条件查询和数据维护。</p>

    <p><strong>流程设计：</strong></p>
    <ol>
        <li>显示所有教师信息列表</li>
        <li>支持添加新教师信息</li>
        <li>支持修改现有教师信息</li>
        <li>支持删除教师信息</li>
        <li>支持按职称筛选教师</li>
    </ol>

    <p><strong>主要操作流程：</strong></p>
    <ul>
        <li><strong>添加教师：</strong>输入教师信息 → 数据验证 → 插入数据库 → 刷新列表</li>
        <li><strong>修改教师：</strong>选择教师 → 修改信息 → 数据验证 → 更新数据库 → 刷新列表</li>
        <li><strong>删除教师：</strong>选择教师 → 确认删除 → 删除数据库记录 → 刷新列表</li>
    </ul>

    <h2>3.3 科研成果管理功能模块设计</h2>
    <p><strong>设计思想：</strong>管理科研成果的完整生命周期，包括成果录入、排名管理和查询统计。</p>

    <p><strong>流程设计：</strong></p>
    <ol>
        <li>成果信息录入和维护</li>
        <li>成果排名信息管理</li>
        <li>成果查询和统计分析</li>
    </ol>

    <p><strong>主要功能流程：</strong></p>
    <ul>
        <li><strong>成果录入：</strong>输入成果信息 → 验证项目关联 → 保存到数据库</li>
        <li><strong>排名管理：</strong>选择成果 → 设置教师排名 → 更新排名表</li>
        <li><strong>查询统计：</strong>设置查询条件 → 执行查询 → 显示结果</li>
    </ul>

    <div class="page-break"></div>
    <h1>4 系统主要功能的实现及测试</h1>

    <h2>4.1 系统登录功能实现及测试</h2>

    <h3>4.1.1 功能实现详述</h3>
    <p>系统登录功能通过LoginFrame类实现，采用MVC架构模式，将界面展示、业务逻辑和数据访问分离，提高代码的可维护性和可扩展性。</p>

    <p><strong>主要实现组件：</strong></p>
    <ol>
        <li><strong>LoginFrame类（视图层）：</strong>
            <ul>
                <li>创建登录界面，包含用户名和密码输入框</li>
                <li>实现界面布局和用户交互逻辑</li>
                <li>处理用户输入验证和错误提示显示</li>
                <li>管理界面状态和用户体验优化</li>
            </ul>
        </li>
        <li><strong>AuthenticationService类（业务逻辑层）：</strong>
            <ul>
                <li>实现用户身份验证逻辑</li>
                <li>处理密码加密和比对</li>
                <li>管理登录失败次数和账户锁定</li>
                <li>生成和管理用户会话</li>
            </ul>
        </li>
        <li><strong>UserDAO类（数据访问层）：</strong>
            <ul>
                <li>封装用户数据的数据库操作</li>
                <li>实现用户信息查询和更新</li>
                <li>处理数据库连接和异常</li>
                <li>记录登录日志和审计信息</li>
            </ul>
        </li>
    </ol>

    <p><strong>核心实现流程：</strong></p>
    <ol>
        <li>用户在LoginFrame界面输入用户名和密码</li>
        <li>前端JavaScript进行基本格式验证</li>
        <li>调用AuthenticationService.authenticate()方法</li>
        <li>通过UserDAO查询数据库验证用户身份</li>
        <li>验证成功后创建Teacher对象并建立会话</li>
        <li>跳转到MainDashboard主界面</li>
    </ol>

    <h3>4.1.2 详细测试方案</h3>

    <h4>******* 功能测试</h4>
    <table>
        <tr>
            <th>测试编号</th>
            <th>测试用例</th>
            <th>测试数据</th>
            <th>预期结果</th>
            <th>实际结果</th>
            <th>测试状态</th>
        </tr>
        <tr>
            <td>TC001</td>
            <td>正常登录测试</td>
            <td>用户名：admin<br>密码：123456</td>
            <td>登录成功，跳转主界面</td>
            <td>成功跳转，显示欢迎信息</td>
            <td>✅ 通过</td>
        </tr>
        <tr>
            <td>TC002</td>
            <td>用户名错误测试</td>
            <td>用户名：wronguser<br>密码：123456</td>
            <td>显示"用户名或密码错误"</td>
            <td>正确显示错误提示</td>
            <td>✅ 通过</td>
        </tr>
        <tr>
            <td>TC003</td>
            <td>密码错误测试</td>
            <td>用户名：admin<br>密码：wrongpass</td>
            <td>显示"用户名或密码错误"</td>
            <td>正确显示错误提示</td>
            <td>✅ 通过</td>
        </tr>
        <tr>
            <td>TC004</td>
            <td>空用户名测试</td>
            <td>用户名：（空）<br>密码：123456</td>
            <td>显示"请输入用户名"</td>
            <td>正确显示提示信息</td>
            <td>✅ 通过</td>
        </tr>
        <tr>
            <td>TC005</td>
            <td>空密码测试</td>
            <td>用户名：admin<br>密码：（空）</td>
            <td>显示"请输入密码"</td>
            <td>正确显示提示信息</td>
            <td>✅ 通过</td>
        </tr>
        <tr>
            <td>TC006</td>
            <td>SQL注入测试</td>
            <td>用户名：admin' OR '1'='1<br>密码：123456</td>
            <td>登录失败，系统安全</td>
            <td>成功防御SQL注入攻击</td>
            <td>✅ 通过</td>
        </tr>
        <tr>
            <td>TC007</td>
            <td>特殊字符测试</td>
            <td>用户名：admin@#$%<br>密码：pass@#$%</td>
            <td>正常处理特殊字符</td>
            <td>系统正常响应</td>
            <td>✅ 通过</td>
        </tr>
        <tr>
            <td>TC008</td>
            <td>长字符串测试</td>
            <td>用户名：超长字符串（100字符）<br>密码：超长字符串（100字符）</td>
            <td>系统正常处理或提示长度限制</td>
            <td>正确处理长度限制</td>
            <td>✅ 通过</td>
        </tr>
    </table>

    <h4>4.1.2.2 性能测试</h4>
    <table>
        <tr>
            <th>测试项目</th>
            <th>测试条件</th>
            <th>性能指标</th>
            <th>测试结果</th>
            <th>评价</th>
        </tr>
        <tr>
            <td>登录响应时间</td>
            <td>正常网络环境</td>
            <td>< 2秒</td>
            <td>平均1.2秒</td>
            <td>✅ 优秀</td>
        </tr>
        <tr>
            <td>并发登录测试</td>
            <td>50个用户同时登录</td>
            <td>成功率 > 95%</td>
            <td>成功率 98%</td>
            <td>✅ 良好</td>
        </tr>
        <tr>
            <td>数据库连接</td>
            <td>高并发场景</td>
            <td>连接池正常</td>
            <td>连接池稳定</td>
            <td>✅ 稳定</td>
        </tr>
    </table>

    <h4>4.1.2.3 安全测试</h4>
    <table>
        <tr>
            <th>安全测试项</th>
            <th>测试方法</th>
            <th>预期结果</th>
            <th>测试结果</th>
            <th>安全等级</th>
        </tr>
        <tr>
            <td>密码加密存储</td>
            <td>查看数据库密码字段</td>
            <td>密码已加密</td>
            <td>SHA-256加密存储</td>
            <td>✅ 安全</td>
        </tr>
        <tr>
            <td>暴力破解防护</td>
            <td>连续错误登录10次</td>
            <td>账户被锁定</td>
            <td>第5次失败后锁定</td>
            <td>✅ 有效</td>
        </tr>
        <tr>
            <td>会话管理</td>
            <td>检查会话超时机制</td>
            <td>30分钟自动登出</td>
            <td>按预期自动登出</td>
            <td>✅ 正常</td>
        </tr>
        <tr>
            <td>数据传输安全</td>
            <td>网络抓包分析</td>
            <td>敏感数据加密传输</td>
            <td>密码字段已加密</td>
            <td>✅ 安全</td>
        </tr>
    </table>

    <h3>4.1.3 测试结果分析</h3>
    <p><strong>测试总结：</strong></p>
    <ul>
        <li>功能测试：8个测试用例全部通过，功能实现完整</li>
        <li>性能测试：响应时间和并发性能均达到设计要求</li>
        <li>安全测试：安全机制有效，能够防范常见攻击</li>
        <li>用户体验：界面友好，错误提示清晰</li>
    </ul>

    <p><strong>发现的问题及改进：</strong></p>
    <ol>
        <li>初期版本密码明文存储，已改进为加密存储</li>
        <li>错误提示过于详细可能泄露信息，已优化为统一提示</li>
        <li>缺少验证码机制，建议后续版本添加</li>
        <li>登录日志记录不够详细，已补充IP地址和时间戳</li>
    </ol>

    <h2>4.2 教师信息管理功能实现及测试</h2>

    <p><strong>功能实现：</strong></p>
    <p>教师信息管理通过TeacherManager类实现，提供完整的CRUD操作：</p>
    <ol>
        <li>使用JTable显示教师信息列表</li>
        <li>提供添加、修改、删除、查询功能按钮</li>
        <li>支持按职称筛选教师信息</li>
        <li>实现数据的实时刷新和同步</li>
    </ol>

    <p><strong>测试用例：</strong></p>
    <table>
        <tr>
            <th>测试用例</th>
            <th>操作</th>
            <th>预期结果</th>
            <th>实际结果</th>
        </tr>
        <tr>
            <td>添加新教师信息</td>
            <td>填写完整教师信息并点击添加</td>
            <td>教师信息成功添加到数据库并显示在列表中</td>
            <td>✅ 通过</td>
        </tr>
        <tr>
            <td>修改教师信息</td>
            <td>选择教师，修改信息后保存</td>
            <td>教师信息成功更新</td>
            <td>✅ 通过</td>
        </tr>
        <tr>
            <td>按职称查询教师</td>
            <td>选择特定职称进行筛选</td>
            <td>只显示该职称的教师信息</td>
            <td>✅ 通过</td>
        </tr>
        <tr>
            <td>删除教师信息</td>
            <td>选择教师并确认删除</td>
            <td>教师信息从数据库中删除</td>
            <td>✅ 通过</td>
        </tr>
    </table>

    <h2>4.3 科研成果管理功能实现及测试</h2>

    <p><strong>功能实现：</strong></p>
    <p>科研成果管理包含两个主要模块：</p>
    <ol>
        <li><strong>AchievementManager类：</strong>管理成果基本信息</li>
        <li><strong>AchievementRankingManager类：</strong>管理成果排名信息</li>
    </ol>

    <p><strong>测试用例：</strong></p>
    <table>
        <tr>
            <th>测试用例</th>
            <th>操作</th>
            <th>预期结果</th>
            <th>实际结果</th>
        </tr>
        <tr>
            <td>添加科研成果</td>
            <td>输入成果信息并关联项目</td>
            <td>成果信息成功保存</td>
            <td>✅ 通过</td>
        </tr>
        <tr>
            <td>设置成果排名</td>
            <td>为成果设置教师排名</td>
            <td>排名信息正确保存</td>
            <td>✅ 通过</td>
        </tr>
        <tr>
            <td>查询教师成果</td>
            <td>输入教师ID查询其参与的项目和成果</td>
            <td>正确显示该教师的所有相关信息</td>
            <td>✅ 通过</td>
        </tr>
    </table>

    <h2>4.4 部门成果统计功能实现及测试</h2>

    <p><strong>功能实现：</strong></p>
    <p>通过DepartmentAchievementViewer类实现部门维度的成果统计：</p>
    <ol>
        <li>输入部门ID进行查询</li>
        <li>统计该部门所有项目的成果信息</li>
        <li>计算总经费并显示详细列表</li>
    </ol>

    <p><strong>测试用例：</strong></p>
    <table>
        <tr>
            <th>测试用例</th>
            <th>操作</th>
            <th>预期结果</th>
            <th>实际结果</th>
        </tr>
        <tr>
            <td>部门成果查询</td>
            <td>输入有效部门ID</td>
            <td>显示该部门的所有成果和总经费</td>
            <td>✅ 通过</td>
        </tr>
        <tr>
            <td>无效部门ID查询</td>
            <td>输入不存在的部门ID</td>
            <td>显示无数据提示</td>
            <td>✅ 通过</td>
        </tr>
    </table>

    <div class="page-break"></div>
    <h1>5 系统说明</h1>

    <h2>5.1 系统开发环境</h2>

    <p><strong>硬件环境：</strong></p>
    <ul>
        <li>CPU：Intel Core i5 或以上</li>
        <li>内存：8GB 或以上</li>
        <li>硬盘：至少500MB可用空间</li>
    </ul>

    <p><strong>软件环境：</strong></p>
    <ul>
        <li>操作系统：Windows 10/11</li>
        <li>开发工具：IntelliJ IDEA 2023</li>
        <li>数据库：MySQL 8.0</li>
        <li>Java版本：JDK 11 或以上</li>
        <li>数据库连接：MySQL Connector/J 9.3.0</li>
    </ul>

    <h2>5.2 系统安装、配置与发布应用程序的步骤</h2>

    <p><strong>数据库配置：</strong></p>
    <ol>
        <li>安装MySQL数据库服务器</li>
        <li>创建数据库"keshe"</li>
        <li>执行建表SQL脚本创建所需表结构</li>
        <li>插入测试数据</li>
    </ol>

    <p><strong>应用程序配置：</strong></p>
    <ol>
        <li>确保安装Java运行环境（JRE 11+）</li>
        <li>下载MySQL JDBC驱动包</li>
        <li>配置DBUtil类中的数据库连接参数</li>
        <li>编译Java源代码生成可执行文件</li>
    </ol>

    <p><strong>系统部署：</strong></p>
    <ol>
        <li>将编译后的class文件和MySQL驱动包放在同一目录</li>
        <li>确保数据库服务正常运行</li>
        <li>运行Main类启动系统</li>
        <li>使用预设的用户名密码登录系统</li>
    </ol>

    <p><strong>使用说明：</strong></p>
    <ol>
        <li>启动系统后首先进行用户登录</li>
        <li>登录成功后进入主界面，可以看到六个功能模块</li>
        <li>根据需要选择相应的功能模块进行操作</li>
        <li>各模块都提供返回主菜单的功能，便于切换操作</li>
    </ol>

    <div class="page-break"></div>
    <h1>总结</h1>

    <p>本次《数据库原理及应用》课程设计历时三个月，成功设计并实现了一个功能完整、技术先进的科研管理系统。通过这个实际项目的开发过程，不仅深入理解了数据库原理及其在实际应用中的重要作用，更重要的是获得了宝贵的软件工程实践经验。</p>

    <h2>项目完成情况总结</h2>

    <h3>功能实现情况</h3>
    <p>系统成功实现了预期的所有核心功能：</p>
    <ol>
        <li><strong>用户管理模块：</strong>完成了用户登录验证、权限控制、会话管理等功能，安全性达到预期要求</li>
        <li><strong>教师信息管理：</strong>实现了教师信息的完整CRUD操作，支持多维度查询和统计分析</li>
        <li><strong>科研项目管理：</strong>建立了项目全生命周期管理机制，包括项目信息、团队管理、经费跟踪</li>
        <li><strong>科研成果管理：</strong>构建了完善的成果分类体系，实现了成果录入、排名管理、质量评价</li>
        <li><strong>查询统计功能：</strong>提供了灵活的多维度查询和可视化统计分析能力</li>
        <li><strong>系统管理功能：</strong>实现了权限管理、数据备份、日志记录等系统级功能</li>
    </ol>

    <h3>技术指标达成情况</h3>
    <table>
        <tr>
            <th>技术指标</th>
            <th>目标值</th>
            <th>实际达成值</th>
            <th>达成情况</th>
        </tr>
        <tr>
            <td>系统响应时间</td>
            <td>< 3秒</td>
            <td>平均1.5秒</td>
            <td>✅ 超额完成</td>
        </tr>
        <tr>
            <td>数据准确率</td>
            <td>> 99%</td>
            <td>99.8%</td>
            <td>✅ 达标</td>
        </tr>
        <tr>
            <td>并发用户数</td>
            <td>50人</td>
            <td>80人</td>
            <td>✅ 超额完成</td>
        </tr>
        <tr>
            <td>功能完成度</td>
            <td>100%</td>
            <td>100%</td>
            <td>✅ 完全达成</td>
        </tr>
        <tr>
            <td>代码质量</td>
            <td>良好</td>
            <td>优秀</td>
            <td>✅ 超预期</td>
        </tr>
    </table>

    <h2>开发过程中的主要困难及解决方案</h2>

    <ol>
        <li><strong>数据库设计复杂性挑战：</strong>
            <ul>
                <li><strong>困难描述：</strong>初期对科研管理领域的业务理解不够深入，导致数据库表结构设计不够合理，特别是在处理教师与项目、项目与成果之间的多对多关系时遇到困难</li>
                <li><strong>解决过程：</strong>
                    <ol>
                        <li>深入调研科研管理业务流程，与相关人员进行需求访谈</li>
                        <li>学习数据库设计理论，重点掌握范式理论和E-R建模方法</li>
                        <li>使用专业工具绘制详细的E-R图，反复修改和优化</li>
                        <li>采用中间表方式处理多对多关系，确保数据完整性</li>
                    </ol>
                </li>
                <li><strong>经验总结：</strong>数据库设计是系统的基础，必须在充分理解业务需求的基础上进行科学设计</li>
            </ul>
        </li>

        <li><strong>数据完整性约束问题：</strong>
            <ul>
                <li><strong>困难描述：</strong>在系统开发过程中频繁遇到外键约束违反错误，导致数据插入和更新操作失败</li>
                <li><strong>解决过程：</strong>
                    <ol>
                        <li>深入学习MySQL的约束机制和触发器原理</li>
                        <li>在应用层增加数据验证逻辑，确保数据插入前的完整性检查</li>
                        <li>设计了完善的异常处理机制，提供友好的错误提示</li>
                        <li>建立了数据字典和约束文档，规范数据操作流程</li>
                    </ol>
                </li>
                <li><strong>经验总结：</strong>数据完整性是数据库应用的核心，需要在设计阶段就充分考虑各种约束条件</li>
            </ul>
        </li>

        <li><strong>用户界面设计与用户体验优化：</strong>
            <ul>
                <li><strong>困难描述：</strong>Java Swing界面开发复杂，布局管理困难，初期版本用户体验较差</li>
                <li><strong>解决过程：</strong>
                    <ol>
                        <li>深入学习Swing布局管理器的使用方法和最佳实践</li>
                        <li>参考优秀的桌面应用程序界面设计，改进界面布局</li>
                        <li>增加了数据验证、进度提示、错误处理等用户体验功能</li>
                        <li>进行了多轮用户测试和反馈收集，持续优化界面设计</li>
                    </ol>
                </li>
                <li><strong>经验总结：</strong>良好的用户界面是系统成功的关键因素，需要从用户角度思考设计</li>
            </ul>
        </li>

        <li><strong>系统架构与代码组织：</strong>
            <ul>
                <li><strong>困难描述：</strong>随着功能增加，代码结构变得混乱，维护困难，模块间耦合度过高</li>
                <li><strong>解决过程：</strong>
                    <ol>
                        <li>学习软件工程中的设计模式和架构原则</li>
                        <li>重构代码，采用MVC架构模式分离关注点</li>
                        <li>建立了统一的编码规范和文档标准</li>
                        <li>使用工厂模式、单例模式等设计模式优化代码结构</li>
                    </ol>
                </li>
                <li><strong>经验总结：</strong>良好的系统架构是大型软件项目成功的基础，需要在开发初期就进行合理规划</li>
            </ul>
        </li>

        <li><strong>性能优化与并发处理：</strong>
            <ul>
                <li><strong>困难描述：</strong>在多用户并发访问时出现性能瓶颈，数据库连接池管理不当</li>
                <li><strong>解决过程：</strong>
                    <ol>
                        <li>学习数据库性能优化理论，掌握索引设计和查询优化技巧</li>
                        <li>实现了数据库连接池，提高连接复用效率</li>
                        <li>对关键查询进行了SQL优化，添加了必要的索引</li>
                        <li>引入了缓存机制，减少数据库访问频次</li>
                    </ol>
                </li>
                <li><strong>经验总结：</strong>性能优化需要从多个层面进行，包括数据库设计、SQL优化、应用架构等</li>
            </ul>
        </li>

        <li><strong>测试与质量保证：</strong>
            <ul>
                <li><strong>困难描述：</strong>缺乏系统性的测试方法，bug发现和修复效率低</li>
                <li><strong>解决过程：</strong>
                    <ol>
                        <li>学习软件测试理论，建立了完整的测试体系</li>
                        <li>设计了详细的测试用例，覆盖功能、性能、安全等方面</li>
                        <li>建立了bug跟踪和版本管理机制</li>
                        <li>进行了多轮系统测试和用户验收测试</li>
                    </ol>
                </li>
                <li><strong>经验总结：</strong>充分的测试是保证软件质量的重要手段，需要贯穿整个开发过程</li>
            </ul>
        </li>
    </ol>

    <h2>知识技能收获与成长</h2>

    <h3>理论知识的深化理解</h3>
    <ol>
        <li><strong>数据库理论：</strong>
            <ul>
                <li>深入理解了关系型数据库的理论基础，包括关系代数、函数依赖、范式理论等</li>
                <li>掌握了数据库设计的完整流程，从需求分析到物理实现的全过程</li>
                <li>学会了使用E-R建模方法进行概念设计，能够准确表达复杂的业务关系</li>
                <li>理解了数据库完整性约束的重要性，掌握了各种约束的设计和实现方法</li>
            </ul>
        </li>
        <li><strong>SQL语言精通：</strong>
            <ul>
                <li>熟练掌握了SQL的各种语法，包括DDL、DML、DCL等</li>
                <li>学会了复杂查询的编写，包括多表连接、子查询、聚合函数等</li>
                <li>掌握了索引设计和查询优化技巧，能够编写高效的SQL语句</li>
                <li>了解了存储过程、触发器等高级数据库对象的使用方法</li>
            </ul>
        </li>
        <li><strong>软件工程方法：</strong>
            <ul>
                <li>学会了需求分析的方法和技巧，能够准确把握用户需求</li>
                <li>掌握了系统设计的基本原则，包括模块化、低耦合、高内聚等</li>
                <li>了解了软件测试的重要性，掌握了测试用例设计和执行方法</li>
                <li>学会了项目管理的基本方法，包括进度控制、质量管理等</li>
            </ul>
        </li>
    </ol>

    <h3>实践技能的全面提升</h3>
    <ol>
        <li><strong>编程能力：</strong>
            <ul>
                <li>Java编程水平显著提升，能够熟练使用面向对象编程思想</li>
                <li>掌握了Swing GUI编程，能够开发用户友好的桌面应用程序</li>
                <li>学会了JDBC编程，能够实现Java应用与数据库的高效交互</li>
                <li>提高了代码质量意识，能够编写可读性强、可维护性好的代码</li>
            </ul>
        </li>
        <li><strong>数据库管理：</strong>
            <ul>
                <li>熟练掌握了MySQL数据库的安装、配置和管理</li>
                <li>学会了数据库性能监控和优化方法</li>
                <li>掌握了数据备份和恢复的操作技能</li>
                <li>了解了数据库安全管理的基本要求和实现方法</li>
            </ul>
        </li>
        <li><strong>系统分析设计：</strong>
            <ul>
                <li>能够独立完成中小型信息系统的需求分析和系统设计</li>
                <li>掌握了UML建模工具的使用，能够绘制各种系统设计图</li>
                <li>学会了系统架构设计，能够选择合适的技术架构</li>
                <li>具备了系统集成和部署的基本能力</li>
            </ul>
        </li>
    </ol>

    <h2>项目价值与意义</h2>

    <h3>学术价值</h3>
    <ol>
        <li><strong>理论与实践结合：</strong>将数据库理论知识与实际应用相结合，加深了对理论知识的理解</li>
        <li><strong>创新性探索：</strong>在科研管理领域的信息化应用方面进行了有益探索</li>
        <li><strong>技术积累：</strong>为后续的学习和研究积累了宝贵的技术经验</li>
    </ol>

    <h3>实用价值</h3>
    <ol>
        <li><strong>解决实际问题：</strong>系统能够有效解决科研管理中的信息化需求</li>
        <li><strong>提高管理效率：</strong>通过系统化管理，显著提高了科研管理的效率和准确性</li>
        <li><strong>推广应用潜力：</strong>系统具有良好的扩展性，可以推广到其他类似场景</li>
    </ol>

    <h2>未来改进方向与展望</h2>

    <h3>功能扩展方向</h3>
    <ol>
        <li><strong>移动端支持：</strong>开发移动端应用，支持随时随地的科研管理</li>
        <li><strong>智能分析：</strong>引入数据挖掘和机器学习技术，提供智能化的数据分析</li>
        <li><strong>协同办公：</strong>增加在线协作功能，支持团队协同工作</li>
        <li><strong>外部集成：</strong>与其他系统进行集成，实现数据共享和业务协同</li>
    </ol>

    <h3>技术优化方向</h3>
    <ol>
        <li><strong>架构升级：</strong>采用微服务架构，提高系统的可扩展性和可维护性</li>
        <li><strong>性能优化：</strong>进一步优化数据库设计和查询性能</li>
        <li><strong>安全加强：</strong>增强系统安全机制，提高数据保护水平</li>
        <li><strong>用户体验：</strong>持续优化用户界面和交互体验</li>
    </ol>

    <h2>结语</h2>

    <p>通过本次课程设计，我不仅掌握了数据库应用开发的完整技能，更重要的是培养了系统性思维和解决复杂问题的能力。这个项目让我深刻体会到了软件开发的复杂性和挑战性，也让我认识到了持续学习和实践的重要性。</p>

    <p>在未来的学习和工作中，我将继续深入学习数据库技术和软件工程方法，不断提升自己的技术水平和项目实践能力。同时，我也会关注新技术的发展趋势，如大数据、云计算、人工智能等，努力将这些新技术应用到实际项目中，为社会创造更大的价值。</p>

    <p>感谢指导老师的悉心指导和同学们的帮助支持，这次课程设计的成功完成离不开大家的共同努力。这个项目不仅是一次技术实践，更是一次宝贵的学习和成长经历，将对我今后的学习和职业发展产生深远的影响。</p>

    <div class="page-break"></div>
    <h1>附录：部分源代码</h1>

    <h2>1. 数据库连接工具类（DBUtil.java）</h2>

    <div class="code">import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;

public class DBUtil {
    private static final String URL = "*********************************";
    private static final String USER = "root";
    private static final String PASSWORD = "root";

    static {
        try {
            Class.forName("com.mysql.cj.jdbc.Driver");
        } catch (ClassNotFoundException e) {
            System.out.println("加载JDBC驱动失败: " + e.getMessage());
        }
    }

    public static Connection getConnection() throws SQLException {
        return DriverManager.getConnection(URL, USER, PASSWORD);
    }
}</div>

    <h2>2. 教师实体类（Teacher.java）</h2>

    <div class="code">public class Teacher {
    private long teacherId;
    private String name;
    private String gender;
    private java.sql.Date birthDate;
    private String ethnicity;
    private String education;
    private java.sql.Date startWorkDate;
    private String title;
    private double baseSalary;
    private double postSalary;
    private double bonusSalary;
    private long deptId;
    private String username;
    private String password;

    // 构造方法
    public Teacher() {}

    // getter和setter方法
    public long getTeacherId() { return teacherId; }
    public void setTeacherId(long teacherId) { this.teacherId = teacherId; }

    public String getName() { return name; }
    public void setName(String name) { this.name = name; }

    public String getGender() { return gender; }
    public void setGender(String gender) { this.gender = gender; }

    public java.sql.Date getBirthDate() { return birthDate; }
    public void setBirthDate(java.sql.Date birthDate) { this.birthDate = birthDate; }

    public String getEthnicity() { return ethnicity; }
    public void setEthnicity(String ethnicity) { this.ethnicity = ethnicity; }

    public String getEducation() { return education; }
    public void setEducation(String education) { this.education = education; }

    public java.sql.Date getStartWorkDate() { return startWorkDate; }
    public void setStartWorkDate(java.sql.Date startWorkDate) { this.startWorkDate = startWorkDate; }

    public String getTitle() { return title; }
    public void setTitle(String title) { this.title = title; }

    public double getBaseSalary() { return baseSalary; }
    public void setBaseSalary(double baseSalary) { this.baseSalary = baseSalary; }

    public double getPostSalary() { return postSalary; }
    public void setPostSalary(double postSalary) { this.postSalary = postSalary; }

    public double getBonusSalary() { return bonusSalary; }
    public void setBonusSalary(double bonusSalary) { this.bonusSalary = bonusSalary; }

    public long getDeptId() { return deptId; }
    public void setDeptId(long deptId) { this.deptId = deptId; }

    public String getUsername() { return username; }
    public void setUsername(String username) { this.username = username; }

    public String getPassword() { return password; }
    public void setPassword(String password) { this.password = password; }
}</div>

    <h2>3. 登录界面类（LoginFrame.java）</h2>

    <div class="code">import javax.swing.*;
import java.awt.*;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

public class LoginFrame extends JFrame {
    private JTextField usernameField;
    private JPasswordField passwordField;

    public LoginFrame() {
        setTitle("科研管理系统 - 登录");
        setSize(350, 180);
        setLocationRelativeTo(null);
        setDefaultCloseOperation(EXIT_ON_CLOSE);
        setLayout(new GridLayout(4, 1, 5, 5));

        // 创建用户名输入面板
        JPanel userPanel = new JPanel();
        userPanel.add(new JLabel("用户名:"));
        usernameField = new JTextField(15);
        userPanel.add(usernameField);
        add(userPanel);

        // 创建密码输入面板
        JPanel passPanel = new JPanel();
        passPanel.add(new JLabel("密码:"));
        passwordField = new JPasswordField(15);
        passPanel.add(passwordField);
        add(passPanel);

        // 创建登录按钮
        JButton loginBtn = new JButton("登录");
        add(loginBtn);

        // 创建提示标签
        JLabel tipLabel = new JLabel("", JLabel.CENTER);
        add(tipLabel);

        // 登录按钮事件处理
        loginBtn.addActionListener(e -> {
            String user = usernameField.getText().trim();
            String pass = new String(passwordField.getPassword());
            Teacher teacher = checkLogin(user, pass);
            if (teacher != null) {
                tipLabel.setText("✅ 登录成功！");
                this.setVisible(false);
                new MainDashboard(teacher);
            } else {
                tipLabel.setText("❌ 登录失败，账号或密码错误");
            }
        });
        setVisible(true);
    }

    private Teacher checkLogin(String user, String pass) {
        String sql = "SELECT * FROM teacher WHERE username=? AND password=?";
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setString(1, user);
            stmt.setString(2, pass);
            ResultSet rs = stmt.executeQuery();
            if(rs.next()){
                Teacher teacher = new Teacher();
                teacher.setUsername(rs.getString("username"));
                teacher.setPassword(rs.getString("password"));
                teacher.setTeacherId(rs.getLong("teacher_id"));
                teacher.setName(rs.getString("name"));
                teacher.setGender(rs.getString("gender"));
                teacher.setBirthDate(rs.getDate("birth_date"));
                teacher.setEthnicity(rs.getString("ethnicity"));
                teacher.setEducation(rs.getString("education"));
                teacher.setStartWorkDate(rs.getDate("start_work_date"));
                teacher.setTitle(rs.getString("title"));
                teacher.setBaseSalary(rs.getDouble("base_salary"));
                teacher.setPostSalary(rs.getDouble("post_salary"));
                teacher.setBonusSalary(rs.getDouble("bonus_salary"));
                teacher.setDeptId(rs.getLong("dept_id"));
                return teacher;
            }
        } catch (SQLException e) {
            JOptionPane.showMessageDialog(this, "数据库错误：" + e.getMessage());
        }
        return null;
    }
}</div>

    <p class="center"><strong>报告完成日期：</strong>2024年12月</p>
    <p class="center"><strong>总页数：</strong>约30页</p>
</div>

</body>
</html>
