# 《数据库原理及应用》课程设计实验报告书

**安徽工业大学计算机科学与技术学院**

**题目：科研管理系统设计与实现**

| 项目 | 内容 |
|------|------|
| 学号 | [请填写学号] |
| 姓名 | [请填写姓名] |
| 专业 | 计算机科学与技术 |
| 班级 | [请填写班级] |
| 指导教师 | [请填写指导教师] |
| 分数 | |

**日期：** 2024年 月

---

## 摘要

本课程设计选择科研管理系统作为开发目标，旨在通过实际项目开发加深对数据库原理及应用的理解。随着高等院校科研活动的日益增多，传统的人工管理方式已无法满足现代科研管理的需求，迫切需要一个高效、准确的信息化管理系统。

本系统主要功能包括教师信息管理、科研项目管理、科研成果管理、成果排名管理、项目参与管理以及多维度的查询统计功能。系统能够实现教师基本信息的录入与维护、科研项目的全生命周期管理、科研成果的分类管理和排名统计，以及基于部门和教师的多角度数据查询分析。

系统采用Java语言开发，使用MySQL数据库存储数据，基于Swing技术构建图形用户界面。经过测试，系统功能完整，性能稳定，用户界面友好，达到了预期的设计目标，能够有效提升科研管理的效率和准确性。

**关键词：** 科研管理；数据库设计；Java；MySQL；Swing界面

---

## 目录

1. [系统需求分析](#1-系统需求分析)
   - 1.1 [系统开发背景](#11-系统开发背景)
   - 1.2 [系统目标](#12-系统目标)
   - 1.3 [功能需求](#13-功能需求)
   - 1.4 [非功能需求](#14-非功能需求)
   - 1.5 [系统约束](#15-系统约束)

2. [数据库的设计与实现](#2-数据库的设计与实现)
   - 2.1 [数据库E-R图](#21-数据库e-r图)
   - 2.2 [表结构设计](#22-表结构设计)
   - 2.3 [数据库物理结构设计](#23-数据库物理结构设计)
   - 2.4 [数据库索引设计](#24-数据库索引设计)

3. [系统主要功能设计](#3-系统主要功能设计)

4. [系统主要功能的实现及测试](#4-系统主要功能的实现及测试)

5. [系统说明](#5-系统说明)

6. [总结](#总结)

7. [附录：部分源代码](#附录部分源代码)

---

## 1 系统需求分析

### 1.1 系统开发背景

随着高等教育事业的快速发展，高校科研活动日益频繁，科研项目数量不断增加，科研成果类型日趋多样化。传统的纸质档案管理和人工统计方式已经无法满足现代科研管理的需求，存在以下问题：

1. **信息管理效率低下**：教师信息、项目信息、成果信息分散管理，查询困难，统计工作量大。
2. **数据准确性难以保证**：人工录入和统计容易出错，数据一致性难以维护。
3. **信息共享困难**：各部门之间信息孤立，缺乏有效的信息共享机制。
4. **决策支持不足**：缺乏有效的数据分析和统计报表，难以为管理决策提供支持。

因此，开发一个集成化的科研管理系统，实现科研信息的数字化管理，提高管理效率和决策水平，具有重要的现实意义。

### 1.2 系统目标

设计并实现一个高效、易用、稳定且可扩展的科研管理系统，满足高校科研管理的日常需求，具体目标如下：

1. **提高管理效率**：通过信息化手段，简化科研管理流程，提高工作效率。
2. **保证数据准确性**：建立完整的数据约束机制，确保数据的准确性和一致性。
3. **增强查询统计能力**：提供多维度的查询和统计功能，支持管理决策。
4. **改善用户体验**：设计友好的用户界面，降低系统使用门槛。

### 1.3 功能需求

#### 用户管理
1. **用户登录验证**：教师通过用户名和密码登录系统，系统验证用户身份。
2. **权限控制**：不同用户具有不同的操作权限，确保数据安全。

#### 教师信息管理
1. **教师信息录入**：录入教师基本信息，包括姓名、性别、出生日期、民族、学历、职称、薪资等。
2. **教师信息维护**：支持教师信息的查询、修改、删除操作。
3. **按职称查询**：支持按职称筛选教师信息。

#### 科研项目管理
1. **项目信息管理**：管理项目基本信息，包括项目名称、负责人、经费等。
2. **项目参与管理**：管理教师参与项目的情况，记录参与角色。

#### 科研成果管理
1. **成果信息录入**：录入科研成果信息，包括成果类别、级别、等级、批准日期等。
2. **成果排名管理**：管理科研成果的排名信息，记录各教师在成果中的排名。
3. **成果信息维护**：支持成果信息的增删改查操作。

#### 查询统计功能
1. **教师项目成果查询**：查询指定教师参与的项目和获得的成果。
2. **部门成果统计**：统计指定部门的科研成果和经费情况。
3. **综合查询**：支持多条件组合查询。

### 1.4 非功能需求

#### 性能需求
1. **响应时间**：系统各项操作响应时间不超过3秒。
2. **并发处理**：支持多用户同时操作，保证系统稳定性。

#### 安全性需求
1. **数据安全**：采用合适的数据存储和备份策略，确保数据安全。
2. **访问控制**：实施用户身份验证，防止非法访问。

#### 可用性需求
1. **界面友好**：提供直观、易用的图形用户界面。
2. **操作简便**：操作流程简单明了，降低学习成本。

#### 兼容性需求
1. **平台兼容**：支持Windows操作系统。
2. **数据库兼容**：兼容MySQL数据库系统。

### 1.5 系统约束

#### 技术约束
1. **开发语言**：使用Java语言进行开发。
2. **数据库系统**：使用MySQL数据库管理系统。
3. **用户界面**：基于Java Swing技术构建图形用户界面。
4. **开发环境**：使用IntelliJ IDEA集成开发环境。

#### 硬件约束
1. **服务器配置**：需要安装MySQL数据库服务器。
2. **客户端配置**：需要安装Java运行环境（JRE）。

---

## 2 数据库的设计与实现

### 2.1 数据库E-R图

系统的实体关系图如下所示：

```
[教师] ——— 参与 ——— [项目] ——— 产生 ——— [成果]
  |                    |                    |
  |                    |                    |
管理                  负责                 排名
  |                    |                    |
  |                    |                    |
[部门]              [经费]              [排名]
```

主要实体及其关系：
- **教师（Teacher）**：系统的核心实体，包含教师的基本信息
- **项目（Project）**：科研项目实体，由教师负责管理
- **成果（Achievement）**：科研成果实体，与项目关联
- **项目参与（ProjectParticipation）**：教师与项目的多对多关系
- **成果排名（AchievementRanking）**：教师在成果中的排名关系

### 2.2 表结构设计

#### Teacher表（教师信息表）
| 字段名 | 数据类型 | 约束 | 备注 |
|--------|----------|------|------|
| teacher_id | BIGINT | PRIMARY KEY, AUTO_INCREMENT | 教师唯一标识符 |
| username | VARCHAR(50) | NOT NULL, UNIQUE | 登录用户名 |
| password | VARCHAR(100) | NOT NULL | 登录密码 |
| name | VARCHAR(100) | NOT NULL | 教师姓名 |
| gender | VARCHAR(10) | | 性别 |
| birth_date | DATE | | 出生日期 |
| ethnicity | VARCHAR(50) | | 民族 |
| education | VARCHAR(100) | | 学历 |
| start_work_date | DATE | | 参加工作日期 |
| title | VARCHAR(100) | | 职称 |
| base_salary | DECIMAL(10,2) | | 基本工资 |
| post_salary | DECIMAL(10,2) | | 岗位工资 |
| bonus_salary | DECIMAL(10,2) | | 奖金 |
| dept_id | BIGINT | | 部门ID |

#### Project表（项目信息表）
| 字段名 | 数据类型 | 约束 | 备注 |
|--------|----------|------|------|
| project_id | INT | PRIMARY KEY, AUTO_INCREMENT | 项目唯一标识符 |
| project_name | VARCHAR(200) | NOT NULL | 项目名称 |
| manager_id | BIGINT | FOREIGN KEY | 项目负责人ID |
| fund | DECIMAL(15,2) | | 项目经费 |
| start_date | DATE | | 项目开始日期 |
| end_date | DATE | | 项目结束日期 |

#### Achievement表（成果信息表）
| 字段名 | 数据类型 | 约束 | 备注 |
|--------|----------|------|------|
| achievement_id | INT | PRIMARY KEY, AUTO_INCREMENT | 成果唯一标识符 |
| project_id | INT | FOREIGN KEY | 关联项目ID |
| category | VARCHAR(100) | NOT NULL | 成果类别 |
| level | VARCHAR(50) | | 成果级别 |
| grade | INT | | 成果等级 |
| approval_date | DATE | | 批准日期 |

#### ProjectParticipation表（项目参与表）
| 字段名 | 数据类型 | 约束 | 备注 |
|--------|----------|------|------|
| teacher_id | BIGINT | FOREIGN KEY | 教师ID |
| project_id | INT | FOREIGN KEY | 项目ID |
| role | VARCHAR(100) | | 参与角色 |
| PRIMARY KEY (teacher_id, project_id) | | | 复合主键 |

#### AchievementRanking表（成果排名表）
| 字段名 | 数据类型 | 约束 | 备注 |
|--------|----------|------|------|
| achievement_id | INT | FOREIGN KEY | 成果ID |
| teacher_id | BIGINT | FOREIGN KEY | 教师ID |
| ranking | INT | NOT NULL | 排名 |
| PRIMARY KEY (achievement_id, teacher_id) | | | 复合主键 |

### 2.3 数据库物理结构设计

#### （1）数据库物理结构图
系统使用MySQL数据库，数据库名称为"keshe"。各表之间的外键关系如下：
- ProjectParticipation.teacher_id → Teacher.teacher_id
- ProjectParticipation.project_id → Project.project_id
- Project.manager_id → Teacher.teacher_id
- Achievement.project_id → Project.project_id
- AchievementRanking.achievement_id → Achievement.achievement_id
- AchievementRanking.teacher_id → Teacher.teacher_id

#### （2）建库建表语句

```sql
-- 创建数据库
CREATE DATABASE keshe CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE keshe;

-- 创建教师表
CREATE TABLE Teacher (
    teacher_id BIGINT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(100) NOT NULL,
    name VARCHAR(100) NOT NULL,
    gender VARCHAR(10),
    birth_date DATE,
    ethnicity VARCHAR(50),
    education VARCHAR(100),
    start_work_date DATE,
    title VARCHAR(100),
    base_salary DECIMAL(10,2),
    post_salary DECIMAL(10,2),
    bonus_salary DECIMAL(10,2),
    dept_id BIGINT
);

-- 创建项目表
CREATE TABLE Project (
    project_id INT AUTO_INCREMENT PRIMARY KEY,
    project_name VARCHAR(200) NOT NULL,
    manager_id BIGINT,
    fund DECIMAL(15,2),
    start_date DATE,
    end_date DATE,
    FOREIGN KEY (manager_id) REFERENCES Teacher(teacher_id)
);

-- 创建成果表
CREATE TABLE Achievement (
    achievement_id INT AUTO_INCREMENT PRIMARY KEY,
    project_id INT,
    category VARCHAR(100) NOT NULL,
    level VARCHAR(50),
    grade INT,
    approval_date DATE,
    FOREIGN KEY (project_id) REFERENCES Project(project_id)
);

-- 创建项目参与表
CREATE TABLE ProjectParticipation (
    teacher_id BIGINT,
    project_id INT,
    role VARCHAR(100),
    PRIMARY KEY (teacher_id, project_id),
    FOREIGN KEY (teacher_id) REFERENCES Teacher(teacher_id),
    FOREIGN KEY (project_id) REFERENCES Project(project_id)
);

-- 创建成果排名表
CREATE TABLE AchievementRanking (
    achievement_id INT,
    teacher_id BIGINT,
    ranking INT NOT NULL,
    PRIMARY KEY (achievement_id, teacher_id),
    FOREIGN KEY (achievement_id) REFERENCES Achievement(achievement_id),
    FOREIGN KEY (teacher_id) REFERENCES Teacher(teacher_id)
);
```

### 2.4 数据库索引设计

为提高查询性能，在以下字段上创建索引：

```sql
-- 在Teacher表上创建索引
CREATE INDEX idx_teacher_dept ON Teacher(dept_id);
CREATE INDEX idx_teacher_title ON Teacher(title);
CREATE INDEX idx_teacher_username ON Teacher(username);

-- 在Project表上创建索引
CREATE INDEX idx_project_manager ON Project(manager_id);
CREATE INDEX idx_project_name ON Project(project_name);

-- 在Achievement表上创建索引
CREATE INDEX idx_achievement_project ON Achievement(project_id);
CREATE INDEX idx_achievement_category ON Achievement(category);

-- 在AchievementRanking表上创建索引
CREATE INDEX idx_ranking_teacher ON AchievementRanking(teacher_id);
CREATE INDEX idx_ranking_achievement ON AchievementRanking(achievement_id);
```

---

## 3 系统主要功能设计

### 3.1 登录功能模块设计

**设计思想：** 系统采用基于用户名和密码的身份验证机制，确保只有合法用户才能访问系统。

**流程设计：**
1. 用户在登录界面输入用户名和密码
2. 系统验证用户名和密码的合法性
3. 查询数据库验证用户身份
4. 验证成功后跳转到主界面，失败则提示错误信息

**时序图：**
```
用户 -> 登录界面: 输入用户名密码
登录界面 -> 数据库: 验证用户信息
数据库 -> 登录界面: 返回验证结果
登录界面 -> 主界面: 登录成功，跳转主界面
登录界面 -> 用户: 登录失败，显示错误信息
```

### 3.2 教师信息管理功能模块设计

**设计思想：** 提供完整的教师信息CRUD操作，支持按条件查询和数据维护。

**流程设计：**
1. 显示所有教师信息列表
2. 支持添加新教师信息
3. 支持修改现有教师信息
4. 支持删除教师信息
5. 支持按职称筛选教师

**主要操作流程：**
- **添加教师：** 输入教师信息 → 数据验证 → 插入数据库 → 刷新列表
- **修改教师：** 选择教师 → 修改信息 → 数据验证 → 更新数据库 → 刷新列表
- **删除教师：** 选择教师 → 确认删除 → 删除数据库记录 → 刷新列表

### 3.3 科研成果管理功能模块设计

**设计思想：** 管理科研成果的完整生命周期，包括成果录入、排名管理和查询统计。

**流程设计：**
1. 成果信息录入和维护
2. 成果排名信息管理
3. 成果查询和统计分析

**主要功能流程：**
- **成果录入：** 输入成果信息 → 验证项目关联 → 保存到数据库
- **排名管理：** 选择成果 → 设置教师排名 → 更新排名表
- **查询统计：** 设置查询条件 → 执行查询 → 显示结果

---

## 4 系统主要功能的实现及测试

### 4.1 系统登录功能实现及测试

**功能实现：**
系统登录功能通过LoginFrame类实现，主要代码逻辑：
1. 创建登录界面，包含用户名和密码输入框
2. 用户点击登录按钮后，获取输入的用户名和密码
3. 调用checkLogin方法验证用户身份
4. 验证成功后创建Teacher对象并跳转到主界面

**测试用例：**
- **测试用例1：** 正确用户名和密码登录
  - 输入：有效的用户名和密码
  - 预期结果：登录成功，跳转到主界面
  - 实际结果：✅ 通过

- **测试用例2：** 错误用户名或密码登录
  - 输入：无效的用户名或密码
  - 预期结果：显示登录失败提示
  - 实际结果：✅ 通过

- **测试用例3：** 空用户名或密码登录
  - 输入：空的用户名或密码
  - 预期结果：显示相应错误提示
  - 实际结果：✅ 通过

### 4.2 教师信息管理功能实现及测试

**功能实现：**
教师信息管理通过TeacherManager类实现，提供完整的CRUD操作：
1. 使用JTable显示教师信息列表
2. 提供添加、修改、删除、查询功能按钮
3. 支持按职称筛选教师信息
4. 实现数据的实时刷新和同步

**测试用例：**
- **测试用例1：** 添加新教师信息
  - 操作：填写完整教师信息并点击添加
  - 预期结果：教师信息成功添加到数据库并显示在列表中
  - 实际结果：✅ 通过

- **测试用例2：** 修改教师信息
  - 操作：选择教师，修改信息后保存
  - 预期结果：教师信息成功更新
  - 实际结果：✅ 通过

- **测试用例3：** 按职称查询教师
  - 操作：选择特定职称进行筛选
  - 预期结果：只显示该职称的教师信息
  - 实际结果：✅ 通过

### 4.3 科研成果管理功能实现及测试

**功能实现：**
科研成果管理包含两个主要模块：
1. **AchievementManager类：** 管理成果基本信息
2. **AchievementRankingManager类：** 管理成果排名信息

**测试用例：**
- **测试用例1：** 添加科研成果
  - 操作：输入成果信息并关联项目
  - 预期结果：成果信息成功保存
  - 实际结果：✅ 通过

- **测试用例2：** 设置成果排名
  - 操作：为成果设置教师排名
  - 预期结果：排名信息正确保存
  - 实际结果：✅ 通过

- **测试用例3：** 查询教师成果
  - 操作：输入教师ID查询其参与的项目和成果
  - 预期结果：正确显示该教师的所有相关信息
  - 实际结果：✅ 通过

### 4.4 部门成果统计功能实现及测试

**功能实现：**
通过DepartmentAchievementViewer类实现部门维度的成果统计：
1. 输入部门ID进行查询
2. 统计该部门所有项目的成果信息
3. 计算总经费并显示详细列表

**测试用例：**
- **测试用例1：** 部门成果查询
  - 操作：输入有效部门ID
  - 预期结果：显示该部门的所有成果和总经费
  - 实际结果：✅ 通过

---

## 5 系统说明

### 5.1 系统开发环境

**硬件环境：**
- CPU：Intel Core i5 或以上
- 内存：8GB 或以上
- 硬盘：至少500MB可用空间

**软件环境：**
- 操作系统：Windows 10/11
- 开发工具：IntelliJ IDEA 2023
- 数据库：MySQL 8.0
- Java版本：JDK 11 或以上
- 数据库连接：MySQL Connector/J 9.3.0

### 5.2 系统安装、配置与发布应用程序的步骤

**数据库配置：**
1. 安装MySQL数据库服务器
2. 创建数据库"keshe"
3. 执行建表SQL脚本创建所需表结构
4. 插入测试数据

**应用程序配置：**
1. 确保安装Java运行环境（JRE 11+）
2. 下载MySQL JDBC驱动包
3. 配置DBUtil类中的数据库连接参数
4. 编译Java源代码生成可执行文件

**系统部署：**
1. 将编译后的class文件和MySQL驱动包放在同一目录
2. 确保数据库服务正常运行
3. 运行Main类启动系统
4. 使用预设的用户名密码登录系统

**使用说明：**
1. 启动系统后首先进行用户登录
2. 登录成功后进入主界面，可以看到六个功能模块
3. 根据需要选择相应的功能模块进行操作
4. 各模块都提供返回主菜单的功能，便于切换操作

---

## 总结

本次课程设计成功实现了一个功能完整的科研管理系统，通过实际开发过程，深入理解了数据库原理及其在实际应用中的重要作用。

**系统开发过程中遇到的主要困难及解决办法：**

1. **数据库设计难题：**
   - 困难：初期对表之间的关系设计不够清晰，特别是多对多关系的处理
   - 解决：通过绘制E-R图，明确实体关系，使用中间表处理多对多关系

2. **外键约束问题：**
   - 困难：在添加数据时经常遇到外键约束违反的错误
   - 解决：在插入数据前增加存在性验证，确保关联数据的完整性

3. **界面设计挑战：**
   - 困难：Swing界面布局复杂，用户体验不够友好
   - 解决：采用合适的布局管理器，优化界面结构和交互逻辑

4. **数据同步问题：**
   - 困难：多个界面之间的数据同步更新
   - 解决：在数据修改后及时刷新相关界面，保持数据一致性

5. **异常处理：**
   - 困难：数据库操作异常处理不够完善
   - 解决：增加try-catch块，提供友好的错误提示信息

**收获与体会：**

通过本次课程设计，不仅掌握了数据库设计的基本原理和方法，还学会了如何将理论知识应用到实际项目开发中。系统虽然功能相对简单，但涵盖了数据库应用开发的主要环节，为今后从事相关工作奠定了良好基础。

同时，也认识到了软件开发的复杂性和系统性，需要在需求分析、系统设计、编码实现、测试调试等各个环节都要认真对待，才能开发出高质量的软件系统。

---

## 附录：部分源代码

### 1. 数据库连接工具类（DBUtil.java）

```java
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;

public class DBUtil {
    private static final String URL = "*********************************";
    private static final String USER = "root";
    private static final String PASSWORD = "root";

    static {
        try {
            Class.forName("com.mysql.cj.jdbc.Driver");
        } catch (ClassNotFoundException e) {
            System.out.println("加载JDBC驱动失败: " + e.getMessage());
        }
    }

    public static Connection getConnection() throws SQLException {
        return DriverManager.getConnection(URL, USER, PASSWORD);
    }
}
```

### 2. 教师实体类（Teacher.java）

```java
public class Teacher {
    private long teacherId;
    private String name;
    private String gender;
    private java.sql.Date birthDate;
    private String ethnicity;
    private String education;
    private java.sql.Date startWorkDate;
    private String title;
    private double baseSalary;
    private double postSalary;
    private double bonusSalary;
    private long deptId;
    private String username;
    private String password;

    // 构造方法
    public Teacher() {}

    // getter和setter方法
    public long getTeacherId() { return teacherId; }
    public void setTeacherId(long teacherId) { this.teacherId = teacherId; }

    public String getName() { return name; }
    public void setName(String name) { this.name = name; }

    // ... 其他getter和setter方法
}
```

### 3. 登录界面类（LoginFrame.java）

```java
import javax.swing.*;
import java.awt.*;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

public class LoginFrame extends JFrame {
    private JTextField usernameField;
    private JPasswordField passwordField;

    public LoginFrame() {
        setTitle("科研管理系统 - 登录");
        setSize(350, 180);
        setLocationRelativeTo(null);
        setDefaultCloseOperation(EXIT_ON_CLOSE);
        setLayout(new GridLayout(4, 1, 5, 5));

        // 创建用户名输入面板
        JPanel userPanel = new JPanel();
        userPanel.add(new JLabel("用户名:"));
        usernameField = new JTextField(15);
        userPanel.add(usernameField);
        add(userPanel);

        // 创建密码输入面板
        JPanel passPanel = new JPanel();
        passPanel.add(new JLabel("密码:"));
        passwordField = new JPasswordField(15);
        passPanel.add(passwordField);
        add(passPanel);

        // 创建登录按钮
        JButton loginBtn = new JButton("登录");
        add(loginBtn);

        // 创建提示标签
        JLabel tipLabel = new JLabel("", JLabel.CENTER);
        add(tipLabel);

        // 登录按钮事件处理
        loginBtn.addActionListener(e -> {
            String user = usernameField.getText().trim();
            String pass = new String(passwordField.getPassword());
            Teacher teacher = checkLogin(user, pass);
            if (teacher != null) {
                tipLabel.setText("✅ 登录成功！");
                this.setVisible(false);
                new MainDashboard(teacher);
            } else {
                tipLabel.setText("❌ 登录失败，账号或密码错误");
            }
        });
        setVisible(true);
    }

    private Teacher checkLogin(String user, String pass) {
        String sql = "SELECT * FROM teacher WHERE username=? AND password=?";
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setString(1, user);
            stmt.setString(2, pass);
            ResultSet rs = stmt.executeQuery();
            if(rs.next()){
                Teacher teacher = new Teacher();
                teacher.setUsername(rs.getString("username"));
                teacher.setPassword(rs.getString("password"));
                teacher.setTeacherId(rs.getLong("teacher_id"));
                teacher.setName(rs.getString("name"));
                // ... 设置其他属性
                return teacher;
            }
        } catch (SQLException e) {
            JOptionPane.showMessageDialog(this, "数据库错误：" + e.getMessage());
        }
        return null;
    }
}
```

### 4. 主界面类（MainDashboard.java）

```java
import javax.swing.*;
import java.awt.*;

public class MainDashboard extends JFrame {
    private Teacher currentTeacher;

    public MainDashboard(Teacher teacher) {
        this.currentTeacher = teacher;

        setTitle("科研管理系统 - 主界面（欢迎 " + currentTeacher.getName() + "）");
        setSize(600, 400);
        setLocationRelativeTo(null);
        setDefaultCloseOperation(EXIT_ON_CLOSE);
        setLayout(new GridLayout(3, 2, 20, 20));

        // 创建功能按钮
        JButton teacherBtn = new JButton("教师信息管理");
        JButton projectBtn = new JButton("项目与参与教师管理");
        JButton achievementBtn = new JButton("科研成果与排名管理");
        JButton queryByTeacherBtn = new JButton("查询某教师成果与项目");
        JButton queryByDeptBtn = new JButton("查询某部门成果与经费");
        JButton logoutBtn = new JButton("退出系统");

        // 添加按钮到界面
        add(teacherBtn);
        add(projectBtn);
        add(achievementBtn);
        add(queryByTeacherBtn);
        add(queryByDeptBtn);
        add(logoutBtn);

        // 绑定按钮事件
        teacherBtn.addActionListener(e -> {
            new TeacherManager(currentTeacher).setVisible(true);
            MainDashboard.this.dispose();
        });

        projectBtn.addActionListener(e -> {
            new ProjectParticipationManager(currentTeacher).setVisible(true);
            MainDashboard.this.dispose();
        });

        achievementBtn.addActionListener(e -> {
            new AchievementManager(currentTeacher).setVisible(true);
            MainDashboard.this.dispose();
        });

        queryByTeacherBtn.addActionListener(e -> {
            new TeacherProjectResultViewer(currentTeacher).setVisible(true);
            MainDashboard.this.dispose();
        });

        queryByDeptBtn.addActionListener(e -> {
            new DepartmentAchievementViewer(currentTeacher).setVisible(true);
            MainDashboard.this.dispose();
        });

        logoutBtn.addActionListener(e -> {
            this.dispose();
            new LoginFrame();
        });

        setVisible(true);
    }
}
```

---

**报告完成日期：** 2024年12月

**总页数：** 约25页
