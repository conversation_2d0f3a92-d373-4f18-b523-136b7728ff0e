import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;

public class DepartmentAchievementViewer extends JFrame {
    private JTextField deptIdField;
    private JButton queryBtn, backBtn;
    private JTable table;
    private DefaultTableModel model;
    private JLabel totalFundLabel;
    private Teacher currentTeacher;  // 当前登录教师

    public DepartmentAchievementViewer(Teacher teacher) {
        this.currentTeacher = teacher;

        setTitle("部门成果与经费查询");
        setSize(900, 450);
        setLocationRelativeTo(null);
        setDefaultCloseOperation(EXIT_ON_CLOSE);
        setLayout(new BorderLayout());

        // 输入栏
        JPanel inputPanel = new JPanel();
        inputPanel.add(new JLabel("部门ID："));
        deptIdField = new JTextField(10);
        inputPanel.add(deptIdField);

        queryBtn = new JButton("查询");
        inputPanel.add(queryBtn);

        totalFundLabel = new JLabel("总经费：0 元");
        inputPanel.add(totalFundLabel);

        backBtn = new JButton("返回主菜单");
        inputPanel.add(backBtn);

        add(inputPanel, BorderLayout.NORTH);

        // 表格区域
        model = new DefaultTableModel(new String[]{
                "项目ID", "项目名称", "成果ID", "类别", "级别", "等级", "经费"
        }, 0);
        table = new JTable(model);
        add(new JScrollPane(table), BorderLayout.CENTER);

        // 绑定事件
        queryBtn.addActionListener(e -> queryDepartmentAchievements());
        backBtn.addActionListener(e -> {
            this.dispose(); // 关闭当前窗口
            new MainDashboard(currentTeacher).setVisible(true); // 返回主菜单
        });
    }

    private void queryDepartmentAchievements() {
        model.setRowCount(0);
        String deptId = deptIdField.getText().trim();
        if (deptId.isEmpty()) {
            JOptionPane.showMessageDialog(this, "请输入部门ID！");
            return;
        }

        String sql = """
            SELECT p.project_id, p.project_name, a.achievement_id, 
                   a.category, a.level, a.grade, p.fund
            FROM Project p
            JOIN Achievement a ON p.project_id = a.project_id
            JOIN Teacher t ON p.manager_id = t.teacher_id
            WHERE t.dept_id = ?
        """;

        try (Connection conn = DBUtil.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setInt(1, Integer.parseInt(deptId));
            ResultSet rs = stmt.executeQuery();

            double totalFund = 0;
            while (rs.next()) {
                double fund = rs.getDouble("fund");
                totalFund += fund;
                model.addRow(new Object[]{
                        rs.getInt("project_id"),
                        rs.getString("project_name"),
                        rs.getInt("achievement_id"),
                        rs.getString("category"),
                        rs.getString("level"),
                        rs.getInt("grade"),
                        fund
                });
            }
            totalFundLabel.setText("总经费：" + totalFund + " 元");
        } catch (Exception e) {
            JOptionPane.showMessageDialog(this, "查询失败：" + e.getMessage());
        }
    }


}
