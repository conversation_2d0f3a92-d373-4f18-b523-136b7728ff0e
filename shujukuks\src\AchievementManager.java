import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.sql.*;

public class AchievementManager extends JFrame {
    private JTextField idField, projectIdField, categoryField, levelField, gradeField, dateField;
    private JTable table;
    private DefaultTableModel model;
    private Teacher currentTeacher; // 当前登录教师

    public AchievementManager(Teacher teacher) {
        this.currentTeacher = teacher;

        setTitle("科研成果管理");
        setSize(900, 400);
        setLocationRelativeTo(null);
        setDefaultCloseOperation(EXIT_ON_CLOSE);
        setLayout(new BorderLayout());

        // 输入面板
        JPanel panel = new JPanel(new GridLayout(3, 5, 5, 5));

        panel.add(new JLabel("成果ID"));
        idField = new JTextField();
        panel.add(idField);

        panel.add(new JLabel("项目ID"));
        projectIdField = new JTextField();
        panel.add(projectIdField);

        panel.add(new JLabel("类别"));
        categoryField = new JTextField();
        panel.add(categoryField);

        panel.add(new JLabel("级别"));
        levelField = new JTextField();
        panel.add(levelField);

        panel.add(new JLabel("等级(1-4)"));
        gradeField = new JTextField();
        panel.add(gradeField);

        panel.add(new JLabel("批准日期(yyyy-MM-dd)"));
        dateField = new JTextField();
        panel.add(dateField);

        // 操作按钮
        JButton addBtn = new JButton("添加成果");
        JButton deleteBtn = new JButton("删除成果");
        JButton refreshBtn = new JButton("刷新");
        JButton backBtn = new JButton("返回主菜单");

        addBtn.addActionListener(e -> addAchievement());
        deleteBtn.addActionListener(e -> deleteAchievement());
        refreshBtn.addActionListener(e -> loadData());
        backBtn.addActionListener(e -> {
            this.dispose();
            new MainDashboard(currentTeacher).setVisible(true);
        });

        panel.add(addBtn);
        panel.add(deleteBtn);
        panel.add(refreshBtn);
        panel.add(backBtn);

        add(panel, BorderLayout.NORTH);

        // 表格展示
        model = new DefaultTableModel(new String[]{"成果ID", "项目ID", "类别", "级别", "等级", "批准日期"}, 0);
        table = new JTable(model);
        table.addMouseListener(new MouseAdapter() {
            public void mouseClicked(MouseEvent e) {
                int row = table.getSelectedRow();
                idField.setText(model.getValueAt(row, 0).toString());
                projectIdField.setText(model.getValueAt(row, 1).toString());
                categoryField.setText(model.getValueAt(row, 2).toString());
                levelField.setText(model.getValueAt(row, 3).toString());
                gradeField.setText(model.getValueAt(row, 4).toString());
                dateField.setText(model.getValueAt(row, 5).toString());
            }
        });

        add(new JScrollPane(table), BorderLayout.CENTER);
        loadData();
    }

    private void loadData() {
        model.setRowCount(0);
        String sql = "SELECT * FROM Achievement";
        try (Connection conn = DBUtil.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {
            while (rs.next()) {
                model.addRow(new Object[]{
                        rs.getInt("achievement_id"),
                        rs.getInt("project_id"),
                        rs.getString("category"),
                        rs.getString("level"),
                        rs.getInt("grade"),
                        rs.getDate("approval_date")
                });
            }
        } catch (SQLException e) {
            showError("加载失败: " + e.getMessage());
        }
    }

    private void addAchievement() {
        try {
            int achievementId = Integer.parseInt(idField.getText().trim());
            int projectId = Integer.parseInt(projectIdField.getText().trim());
            String category = categoryField.getText().trim();
            String level = levelField.getText().trim();
            int grade = Integer.parseInt(gradeField.getText().trim());
            Date approvalDate = Date.valueOf(dateField.getText().trim());

            // 检查 project_id 是否存在
            if (!existsInProjectTable(projectId)) {
                showError("项目ID " + projectId + " 不存在，请先在项目表中添加对应项目！");
                return;
            }

            String sql = "INSERT INTO Achievement (achievement_id, project_id, category, level, grade, approval_date) VALUES (?, ?, ?, ?, ?, ?)";
            try (Connection conn = DBUtil.getConnection();
                 PreparedStatement stmt = conn.prepareStatement(sql)) {
                stmt.setInt(1, achievementId);
                stmt.setInt(2, projectId);
                stmt.setString(3, category);
                stmt.setString(4, level);
                stmt.setInt(5, grade);
                stmt.setDate(6, approvalDate);
                stmt.executeUpdate();
                loadData();
            }

        } catch (NumberFormatException e) {
            showError("请输入正确的数字格式！");
        } catch (IllegalArgumentException e) {
            showError("日期格式应为 yyyy-MM-dd！");
        } catch (SQLException e) {
            showError("添加失败: " + e.getMessage());
        }
    }

    private boolean existsInProjectTable(int projectId) throws SQLException {
        String sql = "SELECT COUNT(*) FROM Project WHERE project_id = ?";
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setInt(1, projectId);
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1) > 0;
                }
            }
        }
        return false;
    }

    private void deleteAchievement() {
        String sql = "DELETE FROM Achievement WHERE achievement_id = ?";
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setInt(1, Integer.parseInt(idField.getText()));
            stmt.executeUpdate();
            loadData();
        } catch (SQLException e) {
            showError("删除失败: " + e.getMessage());
        }
    }

    private void showError(String msg) {
        JOptionPane.showMessageDialog(this, msg, "错误", JOptionPane.ERROR_MESSAGE);
    }
}
