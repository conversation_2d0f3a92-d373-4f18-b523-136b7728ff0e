import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.sql.*;

public class ProjectParticipationManager extends J<PERSON>rame {
    private JTextField teacherIdField, projectIdField, roleField;
    private JTable table;
    private DefaultTableModel model;
    Teacher currentTeacher;

    public ProjectParticipationManager(Teacher teacher) {
        currentTeacher = teacher;
        setTitle("项目参与教师管理");
        setSize(800, 350);
        setLocationRelativeTo(null);
        setDefaultCloseOperation(EXIT_ON_CLOSE);
        setLayout(new BorderLayout());

        // 输入区
        JPanel inputPanel = new JPanel(new GridLayout(2, 5, 5, 5));
        inputPanel.add(new JLabel("教师ID"));
        teacherIdField = new JTextField();
        inputPanel.add(teacherIdField);

        inputPanel.add(new JLabel("项目ID"));
        projectIdField = new JTextField();
        inputPanel.add(projectIdField);

        inputPanel.add(new JLabel("角色"));
        roleField = new JTextField();
        inputPanel.add(roleField);

        JButton addBtn = new JButton("添加参与");
        JButton deleteBtn = new JButton("删除参与");
        JButton refreshBtn = new JButton("刷新");
        JButton backBtn = new JButton("返回");

        addBtn.addActionListener(e -> addParticipation());
        deleteBtn.addActionListener(e -> deleteParticipation());
        refreshBtn.addActionListener(e -> loadData());
        backBtn.addActionListener(e -> back());

        inputPanel.add(addBtn);
        inputPanel.add(deleteBtn);
        inputPanel.add(refreshBtn);
        inputPanel.add(backBtn);

        add(inputPanel, BorderLayout.NORTH);

        // 表格
        model = new DefaultTableModel(new String[]{"教师ID", "项目ID", "角色"}, 0);
        table = new JTable(model);
        table.addMouseListener(new MouseAdapter() {
            public void mouseClicked(MouseEvent e) {
                int row = table.getSelectedRow();
                teacherIdField.setText(model.getValueAt(row, 0).toString());
                projectIdField.setText(model.getValueAt(row, 1).toString());
                roleField.setText(model.getValueAt(row, 2).toString());
            }
        });

        add(new JScrollPane(table), BorderLayout.CENTER);
        loadData();
        setVisible(true);
    }

    private void loadData() {
        model.setRowCount(0);
        String sql = "SELECT * FROM ProjectParticipation";
        try (Connection conn = DBUtil.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {
            while (rs.next()) {
                model.addRow(new Object[]{
                        rs.getInt("teacher_id"),
                        rs.getInt("project_id"),
                        rs.getString("role")
                });
            }
        } catch (SQLException e) {
            showError("加载失败: " + e.getMessage());
        }
    }

    private void addParticipation() {
        try {
            if (teacherIdField.getText().isEmpty() || projectIdField.getText().isEmpty() || roleField.getText().isEmpty()) {
                showError("所有字段不能为空！");
                return;
            }

            int teacherId = Integer.parseInt(teacherIdField.getText());
            int projectId = Integer.parseInt(projectIdField.getText());
            String role = roleField.getText();

            if (!existsInTable("Teacher", "teacher_id", teacherId)) {
                showError("教师ID 不存在！");
                return;
            }
            if (!existsInTable("Project", "project_id", projectId)) {
                showError("项目ID 不存在！");
                return;
            }

            if (isParticipationExist(teacherId, projectId)) {
                showError("该教师已参与该项目。");
                return;
            }

            String sql = "INSERT INTO ProjectParticipation (teacher_id, project_id, role) VALUES (?, ?, ?)";
            try (Connection conn = DBUtil.getConnection();
                 PreparedStatement stmt = conn.prepareStatement(sql)) {
                stmt.setInt(1, teacherId);
                stmt.setInt(2, projectId);
                stmt.setString(3, role);
                stmt.executeUpdate();
                loadData();
            }

        } catch (NumberFormatException e) {
            showError("教师ID和项目ID必须是数字！");
        } catch (SQLException e) {
            showError("添加失败: " + e.getMessage());
        }
    }

    private void deleteParticipation() {
        String sql = "DELETE FROM ProjectParticipation WHERE teacher_id=? AND project_id=?";
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setInt(1, Integer.parseInt(teacherIdField.getText()));
            stmt.setInt(2, Integer.parseInt(projectIdField.getText()));
            stmt.executeUpdate();
            loadData();
        } catch (SQLException e) {
            showError("删除失败: " + e.getMessage());
        }
    }

    private boolean isParticipationExist(int teacherId, int projectId) throws SQLException {
        String sql = "SELECT COUNT(*) FROM ProjectParticipation WHERE teacher_id = ? AND project_id = ?";
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setInt(1, teacherId);
            stmt.setInt(2, projectId);
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1) > 0;
                }
            }
        }
        return false;
    }

    private boolean existsInTable(String tableName, String columnName, int value) throws SQLException {
        String sql = "SELECT COUNT(*) FROM " + tableName + " WHERE " + columnName + " = ?";
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setInt(1, value);
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) return rs.getInt(1) > 0;
            }
        }
        return false;
    }

    private void showError(String msg) {
        JOptionPane.showMessageDialog(this, msg, "错误", JOptionPane.ERROR_MESSAGE);
    }

    private void back() {
        this.dispose();
        new MainDashboard(currentTeacher); // 根据你的系统传入当前登录的教师对象
    }
}
